"""
Subscription permission system similar to require_type() but for subscription features.
This middleware checks if users have the required subscription plan and features.
"""

from typing import Optional, List, Union, Callable
from fastapi import HTTPException, Depends
from sqlalchemy.orm import Session
from datetime import datetime, timezone
import json

from config.session import get_db
from config.deps import get_current_user
from config.security import oauth2_scheme
from config.subscription_plans import (
    SUBSCRIPTION_PLANS, FeaturePermission, PlanType, 
    has_feature_permission, get_plan_limit, is_within_limit,
    get_default_plan, get_plan_config
)
from Models.users import User, UserSubscription, SubscriptionPlan, UserTypeEnum


class SubscriptionError(HTTPException):
    """Custom exception for subscription-related errors"""
    def __init__(self, detail: str, status_code: int = 403):
        super().__init__(status_code=status_code, detail=detail)


def get_user_subscription_info(db: Session, user: User) -> dict:
    """Get user's current subscription information"""
    
    # Get user's subscription from database
    subscription = db.query(UserSubscription).filter(
        UserSubscription.user_id == user.id
    ).first()
    
    if not subscription:
        # No subscription found, assign default plan
        default_plan_config = get_default_plan(user.user_type)
        if not default_plan_config:
            raise SubscriptionError(f"No default plan available for {user.user_type.value} users")
        
        return {
            "has_subscription": False,
            "plan_type": PlanType.BASIC,
            "plan_config": default_plan_config,
            "is_active": True,
            "is_trial": True,
            "usage": {}
        }
    
    # Check if subscription is active
    now = datetime.now(timezone.utc)
    is_active = (
        subscription.status == "active" and
        (subscription.end_date is None or subscription.end_date > now)
    )
    
    if not is_active:
        # Subscription expired, fall back to default plan
        default_plan_config = get_default_plan(user.user_type)
        if not default_plan_config:
            raise SubscriptionError(f"Subscription expired and no default plan available")
        
        return {
            "has_subscription": True,
            "plan_type": PlanType.BASIC,
            "plan_config": default_plan_config,
            "is_active": False,
            "is_expired": True,
            "usage": {}
        }
    
    # Get plan configuration from hardcoded plans
    plan_type = None
    plan_config = None
    
    # Try to match with hardcoded plans
    user_plans = SUBSCRIPTION_PLANS.get(user.user_type, {})
    for pt, config in user_plans.items():
        if subscription.plan and config["id"] == subscription.plan.name.lower().replace(" ", "_"):
            plan_type = pt
            plan_config = config
            break
    
    if not plan_config:
        # Fallback to basic plan if no match found
        plan_type = PlanType.BASIC
        plan_config = get_default_plan(user.user_type)
    
    # Parse usage data
    usage = {}
    if subscription.current_usage:
        try:
            usage = json.loads(subscription.current_usage)
        except:
            usage = {}
    
    return {
        "has_subscription": True,
        "plan_type": plan_type,
        "plan_config": plan_config,
        "is_active": True,
        "subscription_id": subscription.id,
        "usage": usage
    }


def require_subscription_feature(
    feature: FeaturePermission,
    check_limits: Optional[List[str]] = None
) -> Callable:
    """
    Dependency that requires a specific subscription feature.
    Similar to require_type() but for subscription features.
    
    Args:
        feature: The required feature permission
        check_limits: List of limit names to check (e.g., ['max_classrooms'])
    """
    
    def dependency(
        db: Session = Depends(get_db),
        token: str = Depends(oauth2_scheme)
    ):
        # Get current user
        current_user = get_current_user(token, db)
        
        # Get subscription info
        subscription_info = get_user_subscription_info(db, current_user)
        
        # Check if user has the required feature
        plan_type = subscription_info["plan_type"]
        plan_config = subscription_info["plan_config"]
        
        if not has_feature_permission(current_user.user_type, plan_type, feature):
            feature_name = feature.value.replace("_", " ").title()
            plan_name = plan_config.get("name", "current plan")
            
            # Get available plans that have this feature
            available_plans = []
            user_plans = SUBSCRIPTION_PLANS.get(current_user.user_type, {})
            for pt, config in user_plans.items():
                if feature in config.get("features", []):
                    available_plans.append(config["name"])
            
            if available_plans:
                upgrade_message = f" Please upgrade to one of: {', '.join(available_plans)}"
            else:
                upgrade_message = " This feature is not available for your user type."
            
            raise SubscriptionError(
                f"{feature_name} is not available in your {plan_name}.{upgrade_message}"
            )
        
        # Check usage limits if specified
        if check_limits and subscription_info["is_active"]:
            usage = subscription_info["usage"]
            
            for limit_name in check_limits:
                current_usage = usage.get(limit_name, 0)
                if not is_within_limit(current_user.user_type, plan_type, limit_name, current_usage):
                    limit_value = get_plan_limit(current_user.user_type, plan_type, limit_name)
                    limit_display = limit_name.replace("_", " ").replace("max ", "").title()
                    
                    raise SubscriptionError(
                        f"You have reached your {limit_display} limit ({current_usage}/{limit_value}). "
                        f"Please upgrade your subscription to continue."
                    )
        
        return {
            "user": current_user,
            "subscription_info": subscription_info
        }
    
    return dependency


def require_subscription_plan(
    min_plan_type: PlanType,
    user_types: Optional[List[UserTypeEnum]] = None
) -> Callable:
    """
    Dependency that requires a minimum subscription plan level.
    
    Args:
        min_plan_type: Minimum required plan type
        user_types: Optional list of allowed user types
    """
    
    def dependency(
        db: Session = Depends(get_db),
        token: str = Depends(oauth2_scheme)
    ):
        # Get current user
        current_user = get_current_user(token, db)
        
        # Check user type if specified
        if user_types and current_user.user_type not in user_types:
            allowed_types = ", ".join([ut.value for ut in user_types])
            raise SubscriptionError(f"This feature is only available for: {allowed_types}")
        
        # Get subscription info
        subscription_info = get_user_subscription_info(db, current_user)
        
        # Define plan hierarchy
        plan_hierarchy = {
            PlanType.BASIC: 1,
            PlanType.PREMIUM: 2,
            PlanType.PRO: 3,
            PlanType.HOME_TUTOR: 3  # Same level as PRO
        }
        
        current_plan_level = plan_hierarchy.get(subscription_info["plan_type"], 0)
        required_plan_level = plan_hierarchy.get(min_plan_type, 999)
        
        if current_plan_level < required_plan_level:
            current_plan_name = subscription_info["plan_config"].get("name", "current plan")
            required_plan_name = min_plan_type.value.replace("_", " ").title()
            
            raise SubscriptionError(
                f"This feature requires {required_plan_name} plan or higher. "
                f"You currently have {current_plan_name}. Please upgrade your subscription."
            )
        
        return {
            "user": current_user,
            "subscription_info": subscription_info
        }
    
    return dependency


def check_usage_limit(
    db: Session,
    user: User,
    limit_name: str,
    increment: int = 1
) -> bool:
    """
    Check if user can perform an action based on usage limits.
    
    Args:
        db: Database session
        user: User object
        limit_name: Name of the limit to check
        increment: How much to increment (default 1)
    
    Returns:
        True if within limits, False otherwise
    """
    
    subscription_info = get_user_subscription_info(db, user)
    
    if not subscription_info["is_active"]:
        return False
    
    plan_type = subscription_info["plan_type"]
    usage = subscription_info["usage"]
    
    current_usage = usage.get(limit_name, 0)
    new_usage = current_usage + increment
    
    return is_within_limit(user.user_type, plan_type, limit_name, new_usage)


def update_usage_counter(
    db: Session,
    user: User,
    limit_name: str,
    increment: int = 1
) -> bool:
    """
    Update usage counter for a user.
    
    Args:
        db: Database session
        user: User object
        limit_name: Name of the counter to update
        increment: How much to increment (default 1)
    
    Returns:
        True if updated successfully, False otherwise
    """
    
    try:
        # Get user's subscription
        subscription = db.query(UserSubscription).filter(
            UserSubscription.user_id == user.id
        ).first()
        
        if not subscription:
            return False
        
        # Parse current usage
        usage = {}
        if subscription.current_usage:
            try:
                usage = json.loads(subscription.current_usage)
            except:
                usage = {}
        
        # Update counter
        usage[limit_name] = usage.get(limit_name, 0) + increment
        
        # Save back to database
        subscription.current_usage = json.dumps(usage)
        db.commit()
        
        return True
    except Exception as e:
        print(f"Error updating usage counter: {e}")
        db.rollback()
        return False


# Convenience functions for common features
def require_classroom_creation():
    """Require permission to create classrooms"""
    return require_subscription_feature(
        FeaturePermission.CREATE_CLASSROOM,
        check_limits=["max_classrooms"]
    )


def require_exam_creation():
    """Require permission to create exams"""
    return require_subscription_feature(
        FeaturePermission.CREATE_EXAM,
        check_limits=["max_exams_per_month"]
    )


def require_competition_creation():
    """Require permission to create competitions"""
    return require_subscription_feature(FeaturePermission.CREATE_COMPETITION)


def require_ai_features():
    """Require AI question generation features"""
    return require_subscription_feature(
        FeaturePermission.AI_QUESTION_GENERATION,
        check_limits=["ai_questions_per_month"]
    )


def require_home_tutoring():
    """Require home tutoring features"""
    return require_subscription_feature(FeaturePermission.HOME_TUTORING)


def require_advanced_analytics():
    """Require advanced analytics features"""
    return require_subscription_feature(FeaturePermission.ADVANCED_ANALYTICS)


def require_mentor_management():
    """Require mentor management features"""
    return require_subscription_feature(FeaturePermission.MENTOR_MANAGEMENT)


def require_premium_plan():
    """Require premium plan or higher"""
    return require_subscription_plan(PlanType.PREMIUM)


def require_teacher_or_institute():
    """Require teacher or institute user type with subscription check"""
    return require_subscription_plan(
        PlanType.BASIC,
        user_types=[UserTypeEnum.teacher, UserTypeEnum.institute]
    )
