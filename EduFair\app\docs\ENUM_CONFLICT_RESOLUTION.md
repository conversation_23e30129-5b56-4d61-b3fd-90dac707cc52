# Enum Conflict Resolution Guide

## 🚨 **Problem: Enum Type Already Exists**

**Error Message:**
```
sqlalchemy.exc.IntegrityError: (psycopg2.errors.UniqueViolation) duplicate key value violates unique constraint "pg_type_typname_nsp_index"
DETAIL: Key (typname, typnamespace)=(usertypeenum, 17700) already exists.
```

This error occurs when PostgreSQL enum types already exist in the database but Alembic/SQLAlchemy tries to create them again.

---

## 🛠️ **Solution Options (Choose One)**

### **Option 1: Automated Python Fix (Recommended)**

Run the comprehensive fix script:

```bash
cd app
python scripts/fix_enum_migration.py
```

This script will:
- ✅ Detect all existing enum types
- ✅ Safely remove conflicting enums
- ✅ Check alembic state
- ✅ Provide next steps

### **Option 2: Manual SQL Commands**

Connect to your PostgreSQL database and run:

```sql
-- Check what enum types exist
SELECT typname FROM pg_type WHERE typtype = 'e' ORDER BY typname;

-- Drop all conflicting enum types
DROP TYPE IF EXISTS usertypeenum CASCADE;
DROP TYPE IF EXISTS eventstatusenum CASCADE;
DROP TYPE IF EXISTS registrationstatusenum CASCADE;
DROP TYPE IF EXISTS taskstatusenum CASCADE;
DROP TYPE IF EXISTS examstatusenum CASCADE;
DROP TYPE IF EXISTS questiontypeenum CASCADE;
DROP TYPE IF EXISTS difficultylevelenum CASCADE;
DROP TYPE IF EXISTS competitionstatusenum CASCADE;
DROP TYPE IF EXISTS mentorassignmentstatusenum CASCADE;
DROP TYPE IF EXISTS mentorverificationstatusenum CASCADE;
DROP TYPE IF EXISTS associationstatusenum CASCADE;
DROP TYPE IF EXISTS associationtypeenum CASCADE;
```

### **Option 3: Complete Database Reset (Nuclear Option)**

If you're in development and can lose all data:

```bash
# Using the automated script
cd app
chmod +x scripts/reset_database.sh
./scripts/reset_database.sh

# Or manually
dropdb edufair_db
createdb edufair_db
alembic upgrade head
```

---

## 📋 **Step-by-Step Resolution**

### **Step 1: Identify Conflicting Enums**

```sql
-- Connect to your database and run:
SELECT 
    n.nspname as schema_name,
    t.typname as enum_name,
    t.oid as type_oid
FROM pg_type t 
JOIN pg_namespace n ON t.typnamespace = n.oid
WHERE t.typtype = 'e' 
ORDER BY n.nspname, t.typname;
```

### **Step 2: Check What's Using the Enums**

```sql
SELECT 
    table_schema,
    table_name, 
    column_name,
    udt_name as enum_type
FROM information_schema.columns 
WHERE udt_name LIKE '%enum%'
ORDER BY table_schema, table_name, column_name;
```

### **Step 3: Drop Conflicting Enums**

If no tables are using the enums (fresh database):

```sql
-- Drop all enum types
DROP TYPE IF EXISTS usertypeenum CASCADE;
DROP TYPE IF EXISTS eventstatusenum CASCADE;
-- ... (add others as needed)
```

If tables are using the enums:

```sql
-- Drop tables first, then enums
DROP TABLE IF EXISTS users CASCADE;
DROP TABLE IF EXISTS events CASCADE;
-- ... (drop other tables)

-- Then drop enum types
DROP TYPE IF EXISTS usertypeenum CASCADE;
DROP TYPE IF EXISTS eventstatusenum CASCADE;
```

### **Step 4: Reset Alembic State**

```bash
# Mark current state as migrated
alembic stamp head

# Apply migrations
alembic upgrade head
```

### **Step 5: Verify Fix**

```bash
# Test application startup
python main.py
```

---

## 🔍 **Advanced Troubleshooting**

### **Check Enum Types in All Schemas**

```sql
SELECT 
    schemaname,
    tablename,
    attname as column_name,
    typname as enum_type
FROM pg_attribute a
JOIN pg_class c ON a.attrelid = c.oid
JOIN pg_namespace n ON c.relnamespace = n.oid
JOIN pg_type t ON a.atttypid = t.oid
WHERE t.typtype = 'e'
AND a.attnum > 0
AND NOT a.attisdropped
ORDER BY schemaname, tablename, attname;
```

### **Force Drop All Enum Types**

```sql
-- Generate drop commands for all enum types
SELECT 'DROP TYPE IF EXISTS ' || n.nspname || '.' || t.typname || ' CASCADE;'
FROM pg_type t 
JOIN pg_namespace n ON t.typnamespace = n.oid
WHERE t.typtype = 'e' AND t.typname LIKE '%enum%';
```

### **Check Alembic Migration History**

```bash
# See all migrations
alembic history

# See current migration
alembic current

# See pending migrations
alembic show head
```

---

## 🚨 **Common Scenarios & Solutions**

### **Scenario 1: Fresh Database Setup**

```bash
# Clean slate approach
dropdb edufair_db
createdb edufair_db
alembic upgrade head
python scripts/setup_default_subscriptions.py
```

### **Scenario 2: Development Database with Data**

```bash
# Backup first
pg_dump edufair_db > backup.sql

# Fix enums
python scripts/fix_enum_migration.py
alembic stamp head
alembic upgrade head

# If issues persist, restore and try nuclear option
```

### **Scenario 3: Production Database**

```bash
# ALWAYS backup first!
pg_dump edufair_db > production_backup_$(date +%Y%m%d_%H%M%S).sql

# Use the automated fix script
python scripts/fix_enum_migration.py

# Test thoroughly before proceeding
```

### **Scenario 4: Multiple Schema Conflicts**

```sql
-- Check all schemas
SELECT DISTINCT schemaname FROM pg_tables;

-- Drop enums from specific schema
DROP TYPE IF EXISTS schema_name.enum_name CASCADE;
```

---

## 🛡️ **Prevention Tips**

### **1. Proper Migration Workflow**

```bash
# Always check current state before changes
alembic current

# Generate migrations properly
alembic revision --autogenerate -m "Description"

# Review generated migration before applying
# Edit migration file if needed

# Apply migration
alembic upgrade head
```

### **2. Team Coordination**

- Always pull latest migrations before creating new ones
- Coordinate enum changes with team members
- Use descriptive migration messages
- Test migrations on development database first

### **3. Database Maintenance**

```bash
# Regular checks
python scripts/fix_enum_migration.py --check-only

# Keep alembic state clean
alembic history --verbose
```

---

## ✅ **Success Indicators**

You'll know the fix worked when:

- ✅ No more enum constraint violation errors
- ✅ `alembic upgrade head` runs without issues
- ✅ Application starts successfully
- ✅ All database operations work correctly
- ✅ No enum-related errors in logs

---

## 📞 **Emergency Contacts**

If all solutions fail:

1. **Check PostgreSQL version compatibility**
2. **Verify database user permissions**
3. **Check for schema-specific issues**
4. **Consider PostgreSQL server restart**
5. **Review PostgreSQL logs for detailed errors**

The enum conflict should now be completely resolved!
