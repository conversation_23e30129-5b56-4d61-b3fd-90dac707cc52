from fastapi import FastAP<PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.exceptions import RequestValidationError
from fastapi.staticfiles import StaticFiles
from sqlalchemy.exc import SQLAlchemyError
from pydantic import ValidationError

# Import models
from Models.Tasks import *
from Models.ExamSession import *  # Import the new exam session models
from config.session import Base, engine

# Import middleware and error handling
from middleware.error_handler import (
    global_exception_handler,
    handle_business_logic_error,
    BusinessLogicError
)
# Removed custom logging - using uvicorn default logging

# Import routers
from Routes.users import router as user_router
from Routes.health import router as health_router
from Routes.TeacherModule.Classroom import router as classroom_router
from Routes.Exams.Subjects import router as subject_router
from Routes.Exams.Chapters import router as chapter_router
from Routes.Exams.Topics import router as topic_router
from Routes.Exams.Subtopics import router as subtopic_router
from Routes.TeacherModule.Tasks import router as task_router
from Routes.TeacherModule.TeacherProfile import router as teacher_profile_router
from Routes.TeacherSubscription import router as teacher_subscription_router
from Routes.Exams.Plan import router as plan_router
from Routes.TeacherModule.announcement import router as announcement_router
from Routes.TeacherModule.Class import router as class_router
from Routes.Exams.Questions import router as question_router
from Routes.Exams.Exam import router as exam_router
from Routes.Exams.ExamSession import router as exam_session_router
from Routes.Exams.ExamChecking import router as exam_checking_router
from Routes.StudentDashboard import router as student_dashboard_router
from Routes.StudentStatistics import router as student_statistics_router
from Routes.file_upload import router as file_upload_router
from Routes.file_admin import router as file_admin_router
from Routes.Events.Events import router as events_router
from Routes.Events.Payments import router as payments_router
from Routes.Events.Calendar import router as calendar_router
from Routes.Events.Competitions import router as competitions_router
from Routes.Events.CompetitionQuestions import router as competition_questions_router
from Routes.Events.CompetitionSecurity import router as competition_security_router
from Routes.Events.MentorChecking import router as mentor_checking_router
from Routes.Events.CompetitionAnalytics import router as competition_analytics_router
from Routes.Institute.Institute import router as institute_router
from Routes.Institute.Mentor import router as mentor_router
from Routes.Institute.Dashboard import router as institute_dashboard_router
from Routes.Institute.MentorManagement import router as institute_mentor_management_router
from Routes.Institute.EventManagement import router as institute_event_management_router
from Routes.Subscriptions import router as subscriptions_router
from config.redis import init_redis, close_redis, get_redis
import asyncio
from sqlalchemy.orm import Session
from config.session import get_db
from datetime import datetime, timedelta, timezone
from Models.Exam import StudentExamAttempt, StudentExamAnswer
import json
import uuid
from config.mongodb import init_mongodb
from config.config import settings
import os

# Using uvicorn default logging instead of custom logging

# Create FastAPI app
app = FastAPI(
    title="EduFair API",
    description="Educational Platform API with comprehensive monitoring and error handling",
    version="1.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Create the database tables (with error handling for existing enums)
# Note: Tables and enums already exist in production database
try:
    # Only create tables if they don't exist
    from sqlalchemy import inspect
    inspector = inspect(engine)
    existing_tables = inspector.get_table_names()

    if not existing_tables or len(existing_tables) < 5:  # If database is mostly empty
        Base.metadata.create_all(bind=engine)
        print("Database tables created successfully")
    else:
        print(f"Database already has {len(existing_tables)} tables - skipping creation")

except Exception as e:
    if "already exists" in str(e) or "duplicate key" in str(e):
        print(f"Database tables already exist (this is normal): {e}")
    else:
        print(f"Error creating database tables: {e}")
        raise

# Add exception handlers
app.add_exception_handler(Exception, global_exception_handler)
app.add_exception_handler(RequestValidationError, global_exception_handler)
app.add_exception_handler(ValidationError, global_exception_handler)
app.add_exception_handler(SQLAlchemyError, global_exception_handler)
app.add_exception_handler(BusinessLogicError, handle_business_logic_error)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Add frontend origins here
    allow_credentials=True,
    allow_methods=["*"],  # Allow all HTTP methods
    allow_headers=["*"],  # Allow all headers
)

# Create upload directory if it doesn't exist
upload_dir = os.path.join(os.getcwd(), settings.UPLOAD_DIR)
os.makedirs(upload_dir, exist_ok=True)

# Mount static files for serving uploaded files
app.mount(
    settings.STATIC_FILES_URL,
    StaticFiles(directory=upload_dir),
    name="static"
)

# Include health check router first (no authentication required)
app.include_router(health_router, prefix="/api/health", tags=["health"])

# Include other routers
app.include_router(user_router, prefix="/api/users", tags=["users"])
app.include_router(classroom_router, prefix="/api/classrooms", tags=["classrooms"])
app.include_router(subject_router, prefix="/api/subjects", tags=["subjects"])
app.include_router(chapter_router, prefix="/api/chapters", tags=["chapters"])
app.include_router(topic_router, prefix="/api/topics", tags=["topics"])
app.include_router(subtopic_router, prefix="/api/subtopics", tags=["subtopics"])
app.include_router(task_router, prefix="/api/tasks", tags=["tasks"])
app.include_router(teacher_profile_router, prefix="/api/teachers", tags=["teacherProfiles"])
app.include_router(teacher_subscription_router, prefix="/api/teacher-subscriptions", tags=["teacherSubscriptions"])
app.include_router(plan_router, prefix="/api/plans", tags=["plans"])
app.include_router(announcement_router, prefix="/api/announcements", tags=["announcements"])
app.include_router(class_router, prefix="/api/classes", tags=["classes"])
app.include_router(question_router, prefix="/api/questions", tags=["questions"])
app.include_router(exam_router, prefix="/api/exams", tags=["exams"])
app.include_router(exam_session_router, prefix="/api/exams/session", tags=["examSession"])
app.include_router(exam_session_router, tags=["exam-websocket"])  # No prefix for WebSocket routes
app.include_router(exam_checking_router, prefix="/api/exams/checking", tags=["examChecking"])
app.include_router(student_dashboard_router, prefix="/api/student", tags=["studentDashboard"])
app.include_router(student_statistics_router, prefix="/api/student", tags=["studentStatistics"])
app.include_router(file_upload_router, prefix="/api/files", tags=["fileUpload"])
app.include_router(file_admin_router, prefix="/api/admin/files", tags=["fileAdmin"])
app.include_router(events_router, prefix="/api/events", tags=["events"])
app.include_router(payments_router, prefix="/api/payments", tags=["payments"])
app.include_router(calendar_router, prefix="/api", tags=["calendar"])
app.include_router(competitions_router, prefix="/api", tags=["competitions"])
app.include_router(competition_questions_router, prefix="/api", tags=["competition-questions"])
app.include_router(competition_security_router, prefix="/api", tags=["competition-security"])
app.include_router(mentor_checking_router, prefix="/api", tags=["mentor-checking"])
app.include_router(competition_analytics_router, prefix="/api", tags=["competition-analytics"])
app.include_router(institute_router, prefix="/api/institutes", tags=["institutes"])
app.include_router(mentor_router, prefix="/api/mentors", tags=["mentors"])
app.include_router(institute_dashboard_router, prefix="/api/institute/dashboard", tags=["institute-dashboard"])
app.include_router(institute_mentor_management_router, prefix="/api/institute/mentors", tags=["institute-mentors"])
app.include_router(institute_event_management_router, prefix="/api/institute/events", tags=["institute-events"])
app.include_router(subscriptions_router, prefix="/api/subscriptions", tags=["subscriptions"])

# Global variable to store the background task
auto_submit_task = None

async def auto_submit_expired_sessions():
    from Models.Exam import Exam  # avoid circular import
    while True:
        try:
            redis = await get_redis()
            # List all keys matching exam_session:*
            keys = await redis.keys("exam_session:*")
            for key in keys:
                session = await redis.hgetall(key)
                if not session or session.get("status") != "active":
                    continue
                start_time = session.get("start_time")
                duration = int(session.get("duration", "0"))
                if not start_time or not duration:
                    continue
                start_dt = datetime.fromisoformat(start_time)
                end_dt = start_dt + timedelta(seconds=duration)
                if datetime.now(timezone.utc) >= end_dt:
                    # Auto-submit
                    db = next(get_db())
                    try:
                        answers = json.loads(session.get("answers", "{}"))

                        # Ensure UUIDs are properly converted
                        exam_id = session["exam_id"]
                        student_id = session["student_id"]

                        # Convert to UUID if they're strings
                        if isinstance(exam_id, str):
                            exam_id = uuid.UUID(exam_id)
                        if isinstance(student_id, str):
                            student_id = uuid.UUID(student_id)

                        print(f"[AutoSubmit] Creating attempt for exam {exam_id}, student {student_id}")

                        attempt = StudentExamAttempt(
                            id=uuid.uuid4(),
                            exam_id=exam_id,
                            student_id=student_id,
                            started_at=start_dt,
                            completed_at=datetime.now(timezone.utc),
                            is_teacher_checked=False,
                            is_ai_checked=False,
                        )
                        db.add(attempt)
                        db.commit()
                        db.refresh(attempt)

                        # Save answers with comprehensive data and proper UUID conversion
                        current_time = datetime.now(timezone.utc)

                        for qid, ans in answers.items():
                            question_id = uuid.UUID(qid) if isinstance(qid, str) else qid

                            # Handle both simple string answers and complex answer objects
                            if isinstance(ans, dict):
                                answer_text = ans.get('answer', ans.get('text', ''))
                                time_spent = ans.get('time_spent_seconds', None)
                            else:
                                answer_text = str(ans) if ans is not None else ''
                                time_spent = None

                            student_answer = StudentExamAnswer(
                                attempt_id=attempt.id,
                                question_id=question_id,
                                answer=answer_text,  # Main answer field
                                answer_text=answer_text,  # Compatibility field
                                submitted_at=current_time,
                                time_spent_seconds=time_spent
                            )
                            db.add(student_answer)

                        db.commit()
                        await redis.delete(key)
                        print(f"[AutoSubmit] Successfully auto-submitted exam for student {student_id}")

                    except Exception as e:
                        print(f"[AutoSubmit] Error: {e}")
                        db.rollback()
                    finally:
                        db.close()
        except Exception as e:
            print(f"[AutoSubmit] Error: {e}")
        await asyncio.sleep(60)

# Removed auto AI checking - frontend will call API directly when needed

@app.on_event("startup")
async def startup_event():
    """Application startup event handler"""
    print("Starting EduFair application...")

    try:
        # Initialize external services
        await init_redis()
        print("Redis connection initialized")

        await init_mongodb()
        print("MongoDB connection initialized")

        # Start background tasks
        global auto_submit_task
        auto_submit_task = asyncio.create_task(auto_submit_expired_sessions())
        print("Background auto-submit task started")

        print("EduFair application startup completed successfully")

    except Exception as e:
        print(f"Application startup failed: {str(e)}")
        raise


@app.on_event("shutdown")
async def shutdown_event():
    """Application shutdown event handler"""
    print("Shutting down EduFair application...")

    try:
        # Close external connections
        await close_redis()
        print("Redis connection closed")

        # Cancel background tasks
        global auto_submit_task
        if auto_submit_task:
            auto_submit_task.cancel()
            try:
                await auto_submit_task
            except asyncio.CancelledError:
                pass
        print("Background tasks cancelled")

        print("EduFair application shutdown completed")

    except Exception as e:
        print(f"Error during application shutdown: {str(e)}")


