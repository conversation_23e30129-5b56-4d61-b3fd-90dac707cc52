from typing import List, Dict, Any, Optional
import uuid
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc, extract
from fastapi import HTTPException
from datetime import datetime, timezone, timedelta
from decimal import Decimal

# Import Models
from Models.users import User, UserTypeEnum, InstituteProfile, MentorProfile, MentorInstituteAssociation
from Models.Events import Event, EventRegistration, EventStatusEnum, RegistrationStatusEnum
from Models.Competitions import CompetitionMentorAssignment

# Import Schemas
from Schemas.Institute.Dashboard import (
    DashboardSummaryOut, QuickStatsOut, RecentActivityOut, NotificationOut,
    AnalyticsOut, MentorPerformanceMetric, EventEngagementMetric, 
    MentorEffectivenessMetric, GrowthMetricsOut, EventSuccessMetric,
    RecentActivitiesResponse, NotificationsResponse, CustomReportRequest, CustomReportOut
)


def get_dashboard_summary(db: Session, institute_id: uuid.UUID) -> DashboardSummaryOut:
    """Get dashboard summary statistics for institute"""
    
    # Verify institute exists
    institute = db.query(User).filter(
        User.id == institute_id,
        User.user_type == UserTypeEnum.institute
    ).first()
    
    if not institute:
        raise HTTPException(status_code=404, detail="Institute not found")
    
    # Get current date for calculations
    now = datetime.now(timezone.utc)
    current_month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    last_month_start = (current_month_start - timedelta(days=1)).replace(day=1)
    
    # Total mentors associated with institute
    total_mentors = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.institute_id == institute_id
    ).count()
    
    # Active mentors (approved status)
    active_mentors = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.institute_id == institute_id,
        MentorInstituteAssociation.status == "approved"
    ).count()
    
    # Pending mentor applications
    pending_applications = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.institute_id == institute_id,
        MentorInstituteAssociation.status == "pending"
    ).count()
    
    # Total events created by institute
    total_events = db.query(Event).filter(
        Event.institute_id == institute_id
    ).count()
    
    # Upcoming events
    upcoming_events = db.query(Event).filter(
        Event.institute_id == institute_id,
        Event.start_datetime > now,
        Event.status == EventStatusEnum.PUBLISHED
    ).count()

    # Completed events
    completed_events = db.query(Event).filter(
        Event.institute_id == institute_id,
        Event.status == EventStatusEnum.COMPLETED
    ).count()
    
    # Total event attendees across all events
    total_attendees = db.query(func.sum(EventRegistration.quantity)).filter(
        EventRegistration.event_id.in_(
            db.query(Event.id).filter(Event.institute_id == institute_id)
        ),
        EventRegistration.status == RegistrationStatusEnum.CONFIRMED
    ).scalar() or 0
    
    # Calculate monthly growth
    # Mentors growth
    current_month_mentors = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.institute_id == institute_id,
        MentorInstituteAssociation.applied_at >= current_month_start
    ).count()
    
    last_month_mentors = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.institute_id == institute_id,
        MentorInstituteAssociation.applied_at >= last_month_start,
        MentorInstituteAssociation.applied_at < current_month_start
    ).count()
    
    mentor_growth = ((current_month_mentors - last_month_mentors) / max(last_month_mentors, 1)) * 100
    
    # Events growth
    current_month_events = db.query(Event).filter(
        Event.institute_id == institute_id,
        Event.created_at >= current_month_start
    ).count()
    
    last_month_events = db.query(Event).filter(
        Event.institute_id == institute_id,
        Event.created_at >= last_month_start,
        Event.created_at < current_month_start
    ).count()
    
    event_growth = ((current_month_events - last_month_events) / max(last_month_events, 1)) * 100
    
    # Attendees growth (registrations this month vs last month)
    current_month_attendees = db.query(func.sum(EventRegistration.quantity)).filter(
        EventRegistration.event_id.in_(
            db.query(Event.id).filter(Event.institute_id == institute_id)
        ),
        EventRegistration.created_at >= current_month_start,
        EventRegistration.status == RegistrationStatusEnum.CONFIRMED
    ).scalar() or 0

    last_month_attendees = db.query(func.sum(EventRegistration.quantity)).filter(
        EventRegistration.event_id.in_(
            db.query(Event.id).filter(Event.institute_id == institute_id)
        ),
        EventRegistration.created_at >= last_month_start,
        EventRegistration.created_at < current_month_start,
        EventRegistration.status == RegistrationStatusEnum.CONFIRMED
    ).scalar() or 0
    
    attendee_growth = ((current_month_attendees - last_month_attendees) / max(last_month_attendees, 1)) * 100
    
    return DashboardSummaryOut(
        total_mentors=total_mentors,
        active_mentors=active_mentors,
        pending_mentor_applications=pending_applications,
        total_events=total_events,
        upcoming_events=upcoming_events,
        completed_events=completed_events,
        total_event_attendees=total_attendees,
        monthly_growth={
            "mentors": round(mentor_growth, 1),
            "events": round(event_growth, 1),
            "eventAttendees": round(attendee_growth, 1)
        }
    )


def get_quick_stats(db: Session, institute_id: uuid.UUID) -> QuickStatsOut:
    """Get quick statistics for institute dashboard"""
    
    # Get current month range
    now = datetime.now(timezone.utc)
    current_month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)
    
    # Events this month
    this_month_events = db.query(Event).filter(
        Event.institute_id == institute_id,
        Event.created_at >= current_month_start
    ).count()
    
    # New mentors this month
    this_month_new_mentors = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.institute_id == institute_id,
        MentorInstituteAssociation.applied_at >= current_month_start
    ).count()
    
    # Event attendees this month
    this_month_attendees = db.query(func.sum(EventRegistration.quantity)).filter(
        EventRegistration.event_id.in_(
            db.query(Event.id).filter(Event.institute_id == institute_id)
        ),
        EventRegistration.created_at >= current_month_start,
        EventRegistration.status == RegistrationStatusEnum.CONFIRMED
    ).scalar() or 0
    
    # Average event rating (placeholder - would need event feedback system)
    average_event_rating = 4.5  # Placeholder
    
    # Mentor satisfaction score (placeholder - would need mentor feedback system)
    mentor_satisfaction_score = 4.3  # Placeholder
    
    # Total revenue (placeholder - would need payment integration)
    total_revenue = Decimal("0.00")  # Placeholder
    
    return QuickStatsOut(
        this_month_events=this_month_events,
        this_month_new_mentors=this_month_new_mentors,
        this_month_attendees=this_month_attendees,
        average_event_rating=average_event_rating,
        mentor_satisfaction_score=mentor_satisfaction_score,
        total_revenue=total_revenue
    )


def get_recent_activities(
    db: Session, 
    institute_id: uuid.UUID, 
    limit: int = 10
) -> RecentActivitiesResponse:
    """Get recent activities for institute"""
    
    activities = []
    
    # Recent mentor applications
    recent_applications = db.query(MentorInstituteAssociation).options(
        joinedload(MentorInstituteAssociation.mentor)
    ).filter(
        MentorInstituteAssociation.institute_id == institute_id
    ).order_by(desc(MentorInstituteAssociation.applied_at)).limit(limit // 2).all()
    
    for app in recent_applications:
        activities.append(RecentActivityOut(
            id=app.id,
            activity_type="mentor_application",
            title="New Mentor Application",
            description=f"Mentor application from {app.mentor.first_name} {app.mentor.last_name}",
            actor_name=f"{app.mentor.first_name} {app.mentor.last_name}",
            actor_id=app.mentor_id,
            related_entity_id=app.id,
            related_entity_type="mentor_application",
            created_at=app.applied_at,
            priority="normal" if app.status == "pending" else "low"
        ))
    
    # Recent events
    recent_events = db.query(Event).filter(
        Event.institute_id == institute_id
    ).order_by(desc(Event.created_at)).limit(limit // 2).all()
    
    for event in recent_events:
        activities.append(RecentActivityOut(
            id=event.id,
            activity_type="event_created",
            title="New Event Created",
            description=f"Event '{event.title}' was created",
            related_entity_id=event.id,
            related_entity_type="event",
            created_at=event.created_at,
            priority="normal"
        ))
    
    # Sort all activities by creation date
    activities.sort(key=lambda x: x.created_at, reverse=True)
    activities = activities[:limit]
    
    return RecentActivitiesResponse(
        activities=activities,
        total_unread=len([a for a in activities if not a.is_read]),
        has_more=len(activities) == limit
    )


def get_notifications(
    db: Session,
    institute_id: uuid.UUID,
    limit: int = 10,
    unread_only: bool = False
) -> NotificationsResponse:
    """Get notifications for institute"""

    # For now, generate sample notifications based on recent activities
    # In a real implementation, you'd have a notifications table
    notifications = []

    # Check for pending mentor applications
    pending_count = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.institute_id == institute_id,
        MentorInstituteAssociation.status == "pending"
    ).count()

    if pending_count > 0:
        notifications.append(NotificationOut(
            id=uuid.uuid4(),
            title="Pending Mentor Applications",
            message=f"You have {pending_count} pending mentor applications to review",
            notification_type="mentor_application",
            priority="high" if pending_count > 5 else "normal",
            action_url="/institute/mentors/applications",
            action_text="Review Applications",
            created_at=datetime.now(timezone.utc)
        ))

    # Check for upcoming events
    upcoming_events = db.query(Event).filter(
        Event.institute_id == institute_id,
        Event.start_datetime > datetime.now(timezone.utc),
        Event.start_datetime <= datetime.now(timezone.utc) + timedelta(days=7)
    ).count()

    if upcoming_events > 0:
        notifications.append(NotificationOut(
            id=uuid.uuid4(),
            title="Upcoming Events",
            message=f"You have {upcoming_events} events starting within the next week",
            notification_type="event_reminder",
            priority="normal",
            action_url="/institute/events",
            action_text="View Events",
            created_at=datetime.now(timezone.utc)
        ))

    return NotificationsResponse(
        notifications=notifications[:limit],
        total_unread=len([n for n in notifications if not n.is_read]),
        has_more=len(notifications) > limit
    )


def get_analytics(db: Session, institute_id: uuid.UUID) -> AnalyticsOut:
    """Get comprehensive analytics for institute"""

    # Get mentor performance over last 6 months
    mentor_performance = []
    try:
        for i in range(6):
            month_start = (datetime.now(timezone.utc) - timedelta(days=30*i)).replace(day=1)
            month_end = (month_start + timedelta(days=32)).replace(day=1) - timedelta(days=1)

            try:
                active_mentors = db.query(MentorInstituteAssociation).filter(
                    MentorInstituteAssociation.institute_id == institute_id,
                    MentorInstituteAssociation.status.in_(["approved", "active"]),
                    or_(
                        MentorInstituteAssociation.approved_at <= month_end,
                        MentorInstituteAssociation.approved_at.is_(None)  # Handle null approved_at
                    )
                ).count()

                new_applications = db.query(MentorInstituteAssociation).filter(
                    MentorInstituteAssociation.institute_id == institute_id,
                    MentorInstituteAssociation.applied_at >= month_start,
                    MentorInstituteAssociation.applied_at <= month_end
                ).count()

                approved_applications = db.query(MentorInstituteAssociation).filter(
                    MentorInstituteAssociation.institute_id == institute_id,
                    MentorInstituteAssociation.approved_at >= month_start,
                    MentorInstituteAssociation.approved_at <= month_end
                ).count()

                approval_rate = (approved_applications / max(new_applications, 1)) * 100

                mentor_performance.append(MentorPerformanceMetric(
                    month=month_start.strftime("%Y-%m"),
                    active_mentors=active_mentors,
                    new_applications=new_applications,
                    approval_rate=round(approval_rate, 1),
                    average_rating=4.5  # Placeholder
                ))
            except Exception as e:
                print(f"Error processing month {month_start.strftime('%Y-%m')}: {e}")
                # Add a default entry for this month
                mentor_performance.append(MentorPerformanceMetric(
                    month=month_start.strftime("%Y-%m"),
                    active_mentors=0,
                    new_applications=0,
                    approval_rate=0.0,
                    average_rating=4.5
                ))
    except Exception as e:
        print(f"Error getting mentor performance: {e}")
        # Return default data for last 6 months
        for i in range(6):
            month_start = (datetime.now(timezone.utc) - timedelta(days=30*i)).replace(day=1)
            mentor_performance.append(MentorPerformanceMetric(
                month=month_start.strftime("%Y-%m"),
                active_mentors=0,
                new_applications=0,
                approval_rate=0.0,
                average_rating=4.5
            ))

    # Get event engagement statistics
    event_stats = []
    try:
        events = db.query(Event).options(
            joinedload(Event.registrations)
        ).filter(
            Event.institute_id == institute_id,
            Event.status == EventStatusEnum.COMPLETED
        ).order_by(desc(Event.start_datetime)).limit(10).all()

        for event in events:
            try:
                # Safely calculate registrations
                total_registrations = 0
                if event.registrations:
                    total_registrations = sum(
                        reg.quantity for reg in event.registrations
                        if reg.status == RegistrationStatusEnum.CONFIRMED
                    )

                # Placeholder for attendance rate and satisfaction
                attendance_rate = 85.0  # Would need check-in system
                satisfaction_score = 4.2  # Would need feedback system

                event_stats.append(EventEngagementMetric(
                    event_id=event.id,
                    title=event.title or "Untitled Event",
                    attendees=total_registrations,
                    attendance_rate=attendance_rate,
                    satisfaction_score=satisfaction_score,
                    revenue=Decimal("0.00"),  # Placeholder
                    start_date=event.start_datetime
                ))
            except Exception as e:
                print(f"Error processing event {event.id}: {e}")
                continue
    except Exception as e:
        # If there's an error with event queries, just return empty list
        print(f"Error getting event stats: {e}")
        event_stats = []

    # Get mentor effectiveness
    mentor_effectiveness = []
    try:
        mentors = db.query(User).options(
            joinedload(User.mentor_profile)
        ).join(
            MentorInstituteAssociation,
            User.id == MentorInstituteAssociation.mentor_id  # Explicit join condition
        ).filter(
            MentorInstituteAssociation.institute_id == institute_id,
            MentorInstituteAssociation.status.in_(["approved", "active"])  # Handle both possible values
        ).limit(10).all()

        for mentor in mentors:
            if mentor.mentor_profile:
                # Safely handle expertise_areas which might be None or empty
                specialization = None
                if mentor.mentor_profile.expertise_areas and len(mentor.mentor_profile.expertise_areas) > 0:
                    specialization = mentor.mentor_profile.expertise_areas[0]

                mentor_effectiveness.append(MentorEffectivenessMetric(
                    mentor_id=mentor.id,
                    name=f"{mentor.first_name} {mentor.last_name}",
                    specialization=specialization,
                    sessions_completed=25,  # Placeholder
                    average_rating=float(mentor.mentor_profile.rating) if mentor.mentor_profile.rating else 4.0,
                    response_time="2.5 hours",  # Placeholder
                    total_students=15  # Placeholder
                ))
    except Exception as e:
        # If there's an error with mentor queries, just return empty list
        print(f"Error getting mentor effectiveness: {e}")
        mentor_effectiveness = []

    return AnalyticsOut(
        mentor_performance=mentor_performance,
        event_stats=event_stats,
        mentor_effectiveness=mentor_effectiveness
    )
