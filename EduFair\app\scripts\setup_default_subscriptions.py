"""
<PERSON><PERSON><PERSON> to set up hardcoded subscription plans in the database.
This script reads from the hardcoded plans configuration and creates them in the database.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from sqlalchemy.orm import Session
from config.session import get_db
from Models.users import SubscriptionPlan, UserTypeEnum
from config.subscription_plans import SUBSCRIPTION_PLANS, PlanType
import json


def create_hardcoded_plans(db: Session):
    """Create subscription plans from hardcoded configuration"""

    created_count = 0
    updated_count = 0

    for user_type, plans in SUBSCRIPTION_PLANS.items():
        for plan_type, plan_config in plans.items():

            # Check if plan already exists
            existing_plan = db.query(SubscriptionPlan).filter(
                SubscriptionPlan.name == plan_config["name"],
                SubscriptionPlan.target_user_type == user_type
            ).first()

            if existing_plan:
                # Update existing plan
                existing_plan.description = plan_config["description"]
                existing_plan.price = plan_config["price"]
                existing_plan.duration_days = plan_config["duration_days"]
                existing_plan.features = json.dumps(plan_config["features"])
                existing_plan.plan_type = plan_type.value

                # Update limits
                limits = plan_config.get("limits", {})
                existing_plan.max_classrooms = limits.get("max_classrooms")
                existing_plan.max_students_per_classroom = limits.get("max_students_per_classroom")
                existing_plan.max_exams_per_month = limits.get("max_exams_per_month")
                existing_plan.max_questions_per_exam = limits.get("max_questions_per_exam")

                # Update feature flags
                features = plan_config.get("features", [])
                existing_plan.allows_home_tutoring = "home_tutoring" in [f.value for f in features]
                existing_plan.allows_ai_question_generation = "ai_question_generation" in [f.value for f in features]
                existing_plan.allows_advanced_analytics = "advanced_analytics" in [f.value for f in features]
                existing_plan.priority_support = "priority_support" in [f.value for f in features]

                updated_count += 1
                print(f"Updated plan: {plan_config['name']} for {user_type.value}")

            else:
                # Create new plan
                limits = plan_config.get("limits", {})
                features = plan_config.get("features", [])

                new_plan = SubscriptionPlan(
                    name=plan_config["name"],
                    description=plan_config["description"],
                    price=plan_config["price"],
                    duration_days=plan_config["duration_days"],
                    features=json.dumps([f.value for f in features]),
                    is_active=True,
                    plan_type=plan_type.value,
                    target_user_type=user_type,

                    # Limits
                    max_classrooms=limits.get("max_classrooms"),
                    max_students_per_classroom=limits.get("max_students_per_classroom"),
                    max_exams_per_month=limits.get("max_exams_per_month"),
                    max_questions_per_exam=limits.get("max_questions_per_exam"),

                    # Feature flags
                    allows_home_tutoring="home_tutoring" in [f.value for f in features],
                    allows_ai_question_generation="ai_question_generation" in [f.value for f in features],
                    allows_advanced_analytics="advanced_analytics" in [f.value for f in features],
                    priority_support="priority_support" in [f.value for f in features]
                )

                db.add(new_plan)
                created_count += 1
                print(f"Created plan: {plan_config['name']} for {user_type.value}")

    db.commit()
    print(f"\nHardcoded subscription plans setup completed!")
    print(f"Created: {created_count} plans")
    print(f"Updated: {updated_count} plans")


def main():
    """Main function to run the setup"""
    db = next(get_db())
    try:
        create_hardcoded_plans(db)
    except Exception as e:
        print(f"Error setting up hardcoded plans: {e}")
        db.rollback()
        raise
    finally:
        db.close()


if __name__ == "__main__":
    main()
        name="Teacher Home Tutor",
        description="Special plan for teachers offering home tutoring services",
        price=4999,  # $49.99 per month
        duration_days=30,
        features=json.dumps({
            "all_premium_features": True,
            "home_tutoring_listing": True,
            "location_based_search": True,
            "student_matching": True,
            "booking_management": True,
            "payment_processing": True,
            "verified_tutor_badge": True
        }),
        is_active=True,
        plan_type=PlanTypeEnum.HOME_TUTOR,
        target_user_type=UserTypeEnum.teacher,
        max_classrooms=50,
        max_students_per_classroom=200,
        max_exams_per_month=100,
        max_questions_per_exam=500,
        allows_home_tutoring=True,
        allows_ai_question_generation=True,
        allows_advanced_analytics=True,
        priority_support=True
    )
    
    # Institute Plans
    institute_basic = SubscriptionPlan(
        name="Institute Basic",
        description="Basic plan for educational institutes",
        price=0,  # Free trial
        duration_days=30,
        features=json.dumps({
            "basic_institute_features": True,
            "teacher_management": True,
            "basic_competitions": True,
            "basic_analytics": True
        }),
        is_active=True,
        plan_type=PlanTypeEnum.BASIC,
        target_user_type=UserTypeEnum.institute,
        max_classrooms=10,
        max_students_per_classroom=50,
        max_exams_per_month=20,
        allows_ai_question_generation=False,
        allows_advanced_analytics=False,
        priority_support=False
    )
    
    institute_premium = SubscriptionPlan(
        name="Institute Premium",
        description="Premium plan for institutes with advanced features",
        price=9999,  # $99.99 per month
        duration_days=30,
        features=json.dumps({
            "unlimited_teachers": True,
            "advanced_competitions": True,
            "mentor_management": True,
            "advanced_analytics": True,
            "custom_branding": True,
            "api_access": True
        }),
        is_active=True,
        plan_type=PlanTypeEnum.PREMIUM,
        target_user_type=UserTypeEnum.institute,
        max_classrooms=100,
        max_students_per_classroom=500,
        max_exams_per_month=200,
        allows_ai_question_generation=True,
        allows_advanced_analytics=True,
        priority_support=True
    )
    
    # Mentor Plans
    mentor_basic = SubscriptionPlan(
        name="Mentor Basic",
        description="Basic plan for mentors",
        price=0,  # Free
        duration_days=365,
        features=json.dumps({
            "basic_mentoring": True,
            "competition_checking": True,
            "basic_analytics": True
        }),
        is_active=True,
        plan_type=PlanTypeEnum.BASIC,
        target_user_type=UserTypeEnum.mentor,
        allows_advanced_analytics=False,
        priority_support=False
    )
    
    mentor_premium = SubscriptionPlan(
        name="Mentor Premium",
        description="Premium plan for professional mentors",
        price=1999,  # $19.99 per month
        duration_days=30,
        features=json.dumps({
            "advanced_mentoring": True,
            "priority_assignments": True,
            "advanced_analytics": True,
            "payment_processing": True
        }),
        is_active=True,
        plan_type=PlanTypeEnum.PREMIUM,
        target_user_type=UserTypeEnum.mentor,
        allows_advanced_analytics=True,
        priority_support=True
    )
    
    # Admin Plans (for completeness)
    admin_plan = SubscriptionPlan(
        name="Admin Full Access",
        description="Full access plan for administrators",
        price=0,  # Free for admins
        duration_days=365,
        features=json.dumps({
            "full_system_access": True,
            "user_management": True,
            "system_analytics": True,
            "configuration_access": True
        }),
        is_active=True,
        plan_type=PlanTypeEnum.PRO,
        target_user_type=UserTypeEnum.admin,
        allows_ai_question_generation=True,
        allows_advanced_analytics=True,
        priority_support=True
    )
    
    # Add all plans to database
    plans = [
        student_basic,
        teacher_basic, teacher_premium, teacher_home_tutor,
        institute_basic, institute_premium,
        mentor_basic, mentor_premium,
        admin_plan
    ]
    
    for plan in plans:
        # Check if plan already exists
        existing_plan = db.query(SubscriptionPlan).filter(
            SubscriptionPlan.name == plan.name,
            SubscriptionPlan.target_user_type == plan.target_user_type
        ).first()
        
        if not existing_plan:
            db.add(plan)
            print(f"Created plan: {plan.name} for {plan.target_user_type.value}")
        else:
            print(f"Plan already exists: {plan.name} for {plan.target_user_type.value}")
    
    db.commit()
    print("Default subscription plans setup completed!")


def main():
    """Main function to run the setup"""
    db = next(get_db())
    try:
        create_hardcoded_plans(db)
    except Exception as e:
        print(f"Error setting up hardcoded plans: {e}")
        db.rollback()
        raise
    finally:
        db.close()


if __name__ == "__main__":
    main()
