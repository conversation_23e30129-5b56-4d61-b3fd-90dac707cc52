from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from uuid import UUID
from datetime import timed<PERSON><PERSON>
from decimal import Decimal

# Import CRUD functions
from Cruds.Institute.MentorManagement import (
    get_mentor_applications, approve_mentor_application, reject_mentor_application,
    invite_mentor, get_institute_mentors, get_mentor_performance,
    assign_mentor_to_competition
)

# Import Schemas
from Schemas.Institute.MentorManagement import (
    MentorApplicationsResponse, ApplicationApprovalRequest, ApplicationRejectionRequest,
    MentorApplicationOut, MentorInvitationCreate, MentorInvitationOut,
    InstituteMentorsResponse, InstituteMentorOut, MentorPerformanceOut, MentorAssignmentCreate,
    MentorAssignmentOut, MentorActivationRequest, MentorUpdateRequest
)

# Import dependencies
from Models.users import User
from config.session import get_db
from config.security import oauth2_scheme
from config.deps import get_current_user
from config.permission import require_type

router = APIRouter()


# Mentor Applications & Invitations
@router.get("/applications", response_model=MentorApplicationsResponse)
def get_mentor_applications_route(
    status: Optional[str] = Query(None, description="Filter by status (pending, approved, rejected)"),
    skip: int = Query(0, ge=0, description="Number of items to skip"),
    limit: int = Query(20, ge=1, le=100, description="Number of items to return"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get mentor applications for institute"""
    current_user = get_current_user(token, db)
    return get_mentor_applications(db, current_user.id, status, skip, limit)


@router.post("/applications/{app_id}/approve", response_model=MentorApplicationOut)
def approve_application_route(
    app_id: UUID,
    approval_data: ApplicationApprovalRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Approve mentor application"""
    current_user = get_current_user(token, db)
    return approve_mentor_application(db, current_user.id, app_id, approval_data)


@router.post("/applications/{app_id}/reject", response_model=MentorApplicationOut)
def reject_application_route(
    app_id: UUID,
    rejection_data: ApplicationRejectionRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Reject mentor application"""
    current_user = get_current_user(token, db)
    return reject_mentor_application(db, current_user.id, app_id, rejection_data)


@router.post("/invite", response_model=MentorInvitationOut)
def invite_mentor_route(
    invitation_data: MentorInvitationCreate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Invite mentor to join institute"""
    current_user = get_current_user(token, db)
    return invite_mentor(db, current_user.id, invitation_data)


@router.get("/invitations", response_model=List[MentorInvitationOut])
def get_invitations_route(
    status: Optional[str] = Query(None, description="Filter by status"),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get mentor invitations sent by institute"""
    current_user = get_current_user(token, db)
    
    # Import here to avoid circular imports
    from Models.users import MentorInstituteAssociation
    from sqlalchemy.orm import joinedload
    
    # Get invitations (associations with institute_invited type)
    query = db.query(MentorInstituteAssociation).options(
        joinedload(MentorInstituteAssociation.mentor)
    ).filter(
        MentorInstituteAssociation.institute_id == current_user.id,
        MentorInstituteAssociation.association_type == "institute_invited"
    )
    
    if status:
        query = query.filter(MentorInstituteAssociation.status == status)
    
    invitations = query.offset(skip).limit(limit).all()
    
    invitation_list = []
    for inv in invitations:
        mentor = inv.mentor
        invitation_list.append(MentorInvitationOut(
            id=inv.id,
            mentor_id=inv.mentor_id,
            mentor_email=mentor.email if mentor else "<EMAIL>",
            mentor_name=f"{mentor.first_name} {mentor.last_name}" if mentor else None,
            status=inv.status,
            invitation_message=inv.application_message or "",
            proposed_hourly_rate=inv.hourly_rate,
            proposed_hours_per_week=None,  # Would need to store this separately
            expertise_areas_needed=[],  # Would need to store this separately
            invited_at=inv.applied_at,
            responded_at=inv.responded_at,
            expires_at=inv.applied_at + timedelta(days=30) if inv.applied_at else None
        ))
    
    return invitation_list


# ===== NEW SEPARATED ENDPOINTS =====

@router.get("/applications/pending", response_model=MentorApplicationsResponse)
def get_pending_applications_route(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get PENDING mentor applications only"""
    current_user = get_current_user(token, db)
    return get_mentor_applications(db, current_user.id, "pending", skip, limit)


@router.get("/applications/all", response_model=MentorApplicationsResponse)
def get_all_applications_route(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get ALL mentor applications (pending, approved, rejected)"""
    current_user = get_current_user(token, db)
    return get_mentor_applications(db, current_user.id, None, skip, limit)


@router.get("/applications/history", response_model=MentorApplicationsResponse)
def get_applications_history_route(
    status: Optional[str] = Query(None, description="Filter by status (approved, rejected)"),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get processed mentor applications (approved/rejected history)"""
    current_user = get_current_user(token, db)
    # If no status specified, get both approved and rejected
    if not status:
        # Get both approved and rejected applications
        approved = get_mentor_applications(db, current_user.id, "approved", 0, limit//2)
        rejected = get_mentor_applications(db, current_user.id, "rejected", 0, limit//2)

        # Combine results
        all_applications = approved.applications + rejected.applications
        return MentorApplicationsResponse(
            applications=all_applications[:limit],
            total=approved.total + rejected.total,
            pending=0,
            approved=approved.total,
            rejected=rejected.total
        )
    else:
        return get_mentor_applications(db, current_user.id, status, skip, limit)


@router.get("/active", response_model=InstituteMentorsResponse)
def get_active_mentors_route(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get ACTIVE mentors only (approved and working with institute)"""
    current_user = get_current_user(token, db)
    return get_institute_mentors(db, current_user.id, "approved", skip, limit)


@router.get("/invitations/sent", response_model=List[MentorInvitationOut])
def get_sent_invitations_route(
    status: Optional[str] = Query(None, description="Filter by status"),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get invitations SENT by institute to mentors"""
    current_user = get_current_user(token, db)

    # Import here to avoid circular imports
    from Models.users import MentorInstituteAssociation
    from sqlalchemy.orm import joinedload

    # Get invitations (associations with institute_invited type)
    query = db.query(MentorInstituteAssociation).options(
        joinedload(MentorInstituteAssociation.mentor)
    ).filter(
        MentorInstituteAssociation.institute_id == current_user.id,
        MentorInstituteAssociation.association_type == "institute_invited"
    )

    if status:
        query = query.filter(MentorInstituteAssociation.status == status)

    invitations = query.offset(skip).limit(limit).all()

    invitation_list = []
    for inv in invitations:
        mentor = inv.mentor
        invitation_list.append(MentorInvitationOut(
            id=inv.id,
            mentor_id=inv.mentor_id,
            mentor_email=mentor.email if mentor else "<EMAIL>",
            mentor_name=f"{mentor.first_name} {mentor.last_name}" if mentor else None,
            status=inv.status,
            invitation_message=inv.application_message or "",
            proposed_hourly_rate=inv.hourly_rate,
            proposed_hours_per_week=None,  # Would need to store this separately
            expertise_areas_needed=[],  # Would need to store this separately
            invited_at=inv.applied_at,
            responded_at=inv.responded_at,
            expires_at=inv.applied_at + timedelta(days=30) if inv.applied_at else None
        ))

    return invitation_list


@router.get("/applications/received", response_model=List[MentorApplicationOut])
def get_received_applications_route(
    status: Optional[str] = Query("pending", description="Filter by status"),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get applications RECEIVED from mentors (mentor_applied type)"""
    current_user = get_current_user(token, db)

    # Import here to avoid circular imports
    from Models.users import MentorInstituteAssociation
    from sqlalchemy.orm import joinedload

    # Get applications (associations with mentor_applied type)
    query = db.query(MentorInstituteAssociation).options(
        joinedload(MentorInstituteAssociation.mentor).joinedload(User.mentor_profile)
    ).filter(
        MentorInstituteAssociation.institute_id == current_user.id,
        MentorInstituteAssociation.association_type == "mentor_applied"
    )

    if status:
        query = query.filter(MentorInstituteAssociation.status == status)

    applications = query.offset(skip).limit(limit).all()

    application_list = []
    for app in applications:
        mentor = app.mentor
        mentor_profile = mentor.mentor_profile if mentor else None

        application_list.append(MentorApplicationOut(
            id=app.id,
            applicant_id=app.mentor_id,
            applicant_name=f"{mentor.first_name} {mentor.last_name}" if mentor else "Unknown",
            applicant_email=mentor.email if mentor else "<EMAIL>",
            status=app.status,
            application_date=app.applied_at,
            application_message=app.application_message,
            expertise=mentor_profile.expertise_areas if mentor_profile and mentor_profile.expertise_areas else [],
            proposed_hourly_rate=app.hourly_rate,
            availability_hours=mentor_profile.availability_hours.get('total_per_week') if mentor_profile and mentor_profile.availability_hours else None,
            experience_years=mentor_profile.experience_years if mentor_profile else None,
            education=mentor_profile.education if mentor_profile else None,
            current_position=mentor_profile.current_position if mentor_profile else None,
            linkedin_url=mentor_profile.linkedin_url if mentor_profile else None,
            resume_url=mentor_profile.resume_url if mentor_profile else None
        ))

    return application_list


# ===== INSTITUTE MENTOR MANAGEMENT APIs =====

@router.get("/manage", response_model=InstituteMentorsResponse)
def get_all_institute_mentors(
    status: Optional[str] = Query(None, description="Filter by status (approved, active, inactive)"),
    search: Optional[str] = Query(None, description="Search by mentor name or email"),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get ALL mentors within this institute for management purposes"""
    current_user = get_current_user(token, db)

    # Import here to avoid circular imports
    from Models.users import MentorInstituteAssociation, User
    from sqlalchemy.orm import joinedload
    from sqlalchemy import or_

    # Build query for mentors in this institute
    query = db.query(MentorInstituteAssociation).options(
        joinedload(MentorInstituteAssociation.mentor).joinedload(User.mentor_profile)
    ).filter(
        MentorInstituteAssociation.institute_id == current_user.id,
        MentorInstituteAssociation.status.in_(["approved", "active", "inactive"])  # Only actual mentors, not applications
    )

    # Filter by status if provided
    if status:
        query = query.filter(MentorInstituteAssociation.status == status)

    # Search functionality
    if search:
        query = query.join(User, MentorInstituteAssociation.mentor_id == User.id).filter(
            or_(
                User.first_name.ilike(f"%{search}%"),
                User.last_name.ilike(f"%{search}%"),
                User.email.ilike(f"%{search}%")
            )
        )

    # Get total count
    total = query.count()

    # Get mentors with pagination
    associations = query.order_by(MentorInstituteAssociation.approved_at.desc()).offset(skip).limit(limit).all()

    # Convert to response format
    mentor_list = []
    for assoc in associations:
        mentor = assoc.mentor
        mentor_profile = mentor.mentor_profile if mentor else None

        mentor_list.append(InstituteMentorOut(
            id=mentor.id,
            first_name=mentor.first_name,
            last_name=mentor.last_name,
            email=mentor.email,
            phone=mentor.mobile,
            status=assoc.status,
            join_date=assoc.approved_at,
            expertise=mentor_profile.expertise_areas if mentor_profile and mentor_profile.expertise_areas else [],
            hourly_rate=assoc.hourly_rate,
            hours_per_week=mentor_profile.availability_hours.get('total_per_week') if mentor_profile and mentor_profile.availability_hours else None,
            students_assigned=0,  # Placeholder - would need student assignment system
            average_rating=mentor_profile.rating if mentor_profile else None,
            completed_sessions=0,  # Placeholder - would need session tracking
            total_earnings=Decimal("0.00"),  # Placeholder - would need payment tracking
            last_active=mentor.last_login if hasattr(mentor, 'last_login') else None,
            is_available=assoc.status == "approved" or assoc.status == "active"
        ))

    # Get status counts
    active_count = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.institute_id == current_user.id,
        MentorInstituteAssociation.status.in_(["approved", "active"])
    ).count()

    inactive_count = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.institute_id == current_user.id,
        MentorInstituteAssociation.status == "inactive"
    ).count()

    return InstituteMentorsResponse(
        data=mentor_list,
        total=total,
        active=active_count,
        inactive=inactive_count,
        pending=0  # No pending in management view
    )


@router.get("/manage/summary", response_model=dict)
def get_mentor_management_summary(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get summary statistics for mentor management"""
    current_user = get_current_user(token, db)

    # Import here to avoid circular imports
    from Models.users import MentorInstituteAssociation
    from datetime import datetime, timezone, timedelta

    # Get current month
    now = datetime.now(timezone.utc)
    current_month_start = now.replace(day=1, hour=0, minute=0, second=0, microsecond=0)

    # Total mentors in institute
    total_mentors = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.institute_id == current_user.id,
        MentorInstituteAssociation.status.in_(["approved", "active", "inactive"])
    ).count()

    # Active mentors
    active_mentors = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.institute_id == current_user.id,
        MentorInstituteAssociation.status.in_(["approved", "active"])
    ).count()

    # Inactive mentors
    inactive_mentors = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.institute_id == current_user.id,
        MentorInstituteAssociation.status == "inactive"
    ).count()

    # New mentors this month
    new_this_month = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.institute_id == current_user.id,
        MentorInstituteAssociation.approved_at >= current_month_start,
        MentorInstituteAssociation.status.in_(["approved", "active"])
    ).count()

    # Pending applications (separate from mentors)
    pending_applications = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.institute_id == current_user.id,
        MentorInstituteAssociation.status == "pending"
    ).count()

    return {
        "total_mentors": total_mentors,
        "active_mentors": active_mentors,
        "inactive_mentors": inactive_mentors,
        "new_this_month": new_this_month,
        "pending_applications": pending_applications,
        "activity_rate": (active_mentors / max(total_mentors, 1)) * 100
    }


@router.get("/manage/{mentor_id}/details", response_model=dict)
def get_mentor_management_details(
    mentor_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get detailed mentor information for management purposes"""
    current_user = get_current_user(token, db)

    # Import here to avoid circular imports
    from Models.users import User, MentorInstituteAssociation
    from sqlalchemy.orm import joinedload

    # Verify mentor is associated with this institute
    association = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.institute_id == current_user.id,
        MentorInstituteAssociation.mentor_id == mentor_id,
        MentorInstituteAssociation.status.in_(["approved", "active", "inactive"])
    ).first()

    if not association:
        raise HTTPException(status_code=404, detail="Mentor not found in this institute")

    # Get mentor details
    mentor = db.query(User).options(
        joinedload(User.mentor_profile)
    ).filter(User.id == mentor_id).first()

    if not mentor:
        raise HTTPException(status_code=404, detail="Mentor not found")

    # Get mentor's assignments in this institute
    from Models.Competitions import CompetitionMentorAssignment
    assignments = db.query(CompetitionMentorAssignment).filter(
        CompetitionMentorAssignment.mentor_id == mentor_id,
        CompetitionMentorAssignment.assigned_by == current_user.id
    ).count()

    return {
        "mentor_info": {
            "id": mentor.id,
            "first_name": mentor.first_name,
            "last_name": mentor.last_name,
            "email": mentor.email,
            "phone": mentor.mobile,
            "profile_picture": mentor.profile_picture_url if hasattr(mentor, 'profile_picture_url') else None
        },
        "mentor_profile": {
            "expertise_areas": mentor.mentor_profile.expertise_areas if mentor.mentor_profile else [],
            "experience_years": mentor.mentor_profile.experience_years if mentor.mentor_profile else None,
            "education": mentor.mentor_profile.education if mentor.mentor_profile else None,
            "current_position": mentor.mentor_profile.current_position if mentor.mentor_profile else None,
            "rating": mentor.mentor_profile.rating if mentor.mentor_profile else None,
            "bio": mentor.mentor_profile.bio if mentor.mentor_profile else None,
            "linkedin_url": mentor.mentor_profile.linkedin_url if mentor.mentor_profile else None,
            "availability_hours": mentor.mentor_profile.availability_hours if mentor.mentor_profile else {}
        },
        "institute_association": {
            "status": association.status,
            "join_date": association.approved_at,
            "hourly_rate": association.hourly_rate,
            "contract_terms": association.contract_terms,
            "performance_notes": association.notes,
            "total_assignments": assignments
        },
        "statistics": {
            "completed_sessions": 0,  # Placeholder
            "total_earnings": Decimal("0.00"),  # Placeholder
            "student_satisfaction": 4.5,  # Placeholder
            "response_time_hours": 2.5  # Placeholder
        }
    }


@router.put("/manage/{mentor_id}/status", response_model=dict)
def update_mentor_status(
    mentor_id: UUID,
    status_data: MentorActivationRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Update mentor status within the institute"""
    current_user = get_current_user(token, db)

    # Import here to avoid circular imports
    from Models.users import MentorInstituteAssociation
    from datetime import datetime, timezone

    # Get association
    association = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.institute_id == current_user.id,
        MentorInstituteAssociation.mentor_id == mentor_id,
        MentorInstituteAssociation.status.in_(["approved", "active", "inactive"])
    ).first()

    if not association:
        raise HTTPException(status_code=404, detail="Mentor not found in this institute")

    # Update status
    old_status = association.status
    new_status = "active" if status_data.is_active else "inactive"
    association.status = new_status
    association.updated_at = datetime.now(timezone.utc)

    # Add note about status change
    if status_data.reason:
        association.notes = f"{association.notes or ''}\n[{datetime.now().strftime('%Y-%m-%d')}] Status changed from {old_status} to {new_status}: {status_data.reason}"

    db.commit()

    return {
        "message": f"Mentor status updated from {old_status} to {new_status}",
        "mentor_id": str(mentor_id),
        "old_status": old_status,
        "new_status": new_status,
        "updated_at": datetime.now(timezone.utc)
    }


# Mentor Management - ACTIVE MENTORS ONLY
@router.get("", response_model=InstituteMentorsResponse)
def get_institute_mentors_route(
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get ACTIVE mentors associated with institute (approved/active status only)"""
    current_user = get_current_user(token, db)
    # Only get approved/active mentors, not applications
    return get_institute_mentors(db, current_user.id, "approved", skip, limit)


@router.get("/{mentor_id}", response_model=dict)
def get_mentor_details_route(
    mentor_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get detailed mentor information"""
    current_user = get_current_user(token, db)
    
    # Import here to avoid circular imports
    from Models.users import User, MentorInstituteAssociation
    from sqlalchemy.orm import joinedload
    
    # Verify mentor is associated with institute
    association = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.institute_id == current_user.id,
        MentorInstituteAssociation.mentor_id == mentor_id
    ).first()
    
    if not association:
        raise HTTPException(status_code=404, detail="Mentor not found or not associated with institute")
    
    # Get mentor details
    mentor = db.query(User).options(
        joinedload(User.mentor_profile)
    ).filter(User.id == mentor_id).first()
    
    if not mentor:
        raise HTTPException(status_code=404, detail="Mentor not found")
    
    return {
        "id": mentor.id,
        "first_name": mentor.first_name,
        "last_name": mentor.last_name,
        "email": mentor.email,
        "phone": mentor.mobile,
        "profile": mentor.mentor_profile.__dict__ if mentor.mentor_profile else None,
        "association": {
            "status": association.status,
            "join_date": association.approved_at,
            "hourly_rate": association.hourly_rate,
            "contract_terms": association.contract_terms
        }
    }


@router.put("/{mentor_id}", response_model=dict)
def update_mentor_route(
    mentor_id: UUID,
    update_data: MentorUpdateRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Update mentor details for institute"""
    current_user = get_current_user(token, db)
    
    # Import here to avoid circular imports
    from Models.users import MentorInstituteAssociation
    
    # Get association
    association = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.institute_id == current_user.id,
        MentorInstituteAssociation.mentor_id == mentor_id
    ).first()
    
    if not association:
        raise HTTPException(status_code=404, detail="Mentor not found or not associated with institute")
    
    # Update association fields
    if update_data.hourly_rate is not None:
        association.hourly_rate = update_data.hourly_rate
    if update_data.contract_terms is not None:
        association.contract_terms = update_data.contract_terms
    
    db.commit()
    
    return {"message": "Mentor updated successfully"}


@router.post("/{mentor_id}/activate", response_model=dict)
def activate_mentor_route(
    mentor_id: UUID,
    activation_data: MentorActivationRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Activate or deactivate mentor"""
    current_user = get_current_user(token, db)
    
    # Import here to avoid circular imports
    from Models.users import MentorInstituteAssociation
    
    # Get association
    association = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.institute_id == current_user.id,
        MentorInstituteAssociation.mentor_id == mentor_id
    ).first()
    
    if not association:
        raise HTTPException(status_code=404, detail="Mentor not found or not associated with institute")
    
    # Update status
    new_status = "approved" if activation_data.is_active else "inactive"
    association.status = new_status
    
    db.commit()
    
    return {
        "message": f"Mentor {'activated' if activation_data.is_active else 'deactivated'} successfully",
        "status": new_status
    }


@router.post("/{mentor_id}/deactivate", response_model=dict)
def deactivate_mentor_route(
    mentor_id: UUID,
    activation_data: MentorActivationRequest,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Deactivate mentor"""
    activation_data.is_active = False
    return activate_mentor_route(mentor_id, activation_data, db, token, _)


@router.get("/{mentor_id}/performance", response_model=MentorPerformanceOut)
def get_mentor_performance_route(
    mentor_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get mentor performance metrics"""
    current_user = get_current_user(token, db)
    return get_mentor_performance(db, current_user.id, mentor_id)


@router.get("/{mentor_id}/assignments", response_model=List[MentorAssignmentOut])
def get_mentor_assignments_route(
    mentor_id: UUID,
    status: Optional[str] = Query(None, description="Filter by assignment status"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get mentor's competition assignments"""
    current_user = get_current_user(token, db)
    
    # Import here to avoid circular imports
    from Models.Competitions import CompetitionMentorAssignment
    from Models.Events import Event
    from Models.users import User
    from sqlalchemy.orm import joinedload
    
    # Verify mentor is associated with institute
    from Models.users import MentorInstituteAssociation
    association = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.institute_id == current_user.id,
        MentorInstituteAssociation.mentor_id == mentor_id
    ).first()
    
    if not association:
        raise HTTPException(status_code=404, detail="Mentor not found or not associated with institute")
    
    # Get assignments
    query = db.query(CompetitionMentorAssignment).options(
        joinedload(CompetitionMentorAssignment.competition),
        joinedload(CompetitionMentorAssignment.mentor)
    ).filter(CompetitionMentorAssignment.mentor_id == mentor_id)
    
    if status:
        query = query.filter(CompetitionMentorAssignment.status == status)
    
    assignments = query.all()
    
    assignment_list = []
    for assignment in assignments:
        mentor = assignment.mentor
        competition = assignment.competition
        
        assignment_list.append(MentorAssignmentOut(
            id=assignment.id,
            mentor_id=mentor_id,
            mentor_name=f"{mentor.first_name} {mentor.last_name}",
            competition_id=assignment.competition_id,
            competition_title=competition.title if competition else "Unknown",
            status=assignment.status.value,
            assigned_at=assignment.assigned_at,
            accepted_at=assignment.accepted_at,
            completed_at=assignment.completed_at,
            estimated_hours=assignment.estimated_hours,
            actual_hours=assignment.actual_hours,
            progress_percentage=assignment.progress_percentage or 0.0,
            questions_assigned=assignment.total_questions or 0,
            questions_checked=assignment.questions_checked or 0
        ))
    
    return assignment_list


@router.post("/{mentor_id}/assign-competition", response_model=MentorAssignmentOut)
def assign_competition_route(
    mentor_id: UUID,
    assignment_data: MentorAssignmentCreate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Assign mentor to competition"""
    current_user = get_current_user(token, db)
    return assign_mentor_to_competition(db, current_user.id, mentor_id, assignment_data)
