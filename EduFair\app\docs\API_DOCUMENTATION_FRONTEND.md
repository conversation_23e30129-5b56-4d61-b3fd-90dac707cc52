# EduFair API Documentation - Recent Subscription System`

## 🔐 **Authentication**
All protected endpoints require a Bearer token:
```http
Authorization: Bearer <your_jwt_token>
```

---

## 📚 **Subscription System**

### **Get Available Plans**
```http
GET /api/subscriptions/plans
```

**Query Parameters:**
- `user_type` (optional): `student` | `teacher` | `institute` | `mentor`
- `is_active` (optional): `true` | `false` (default: `true`)
- `skip` (optional): Pagination offset (default: `0`)
- `limit` (optional): Items per page (default: `100`)

**Example Request:**
```http
GET /api/subscriptions/plans?user_type=teacher&is_active=true
```

**Response (200):**
```json
[
  {
    "id": "550e8400-e29b-41d4-a716-446655440001",
    "name": "Teacher Premium",
    "description": "Premium plan for teachers with advanced features",
    "price": 2999,
    "duration_days": 30,
    "features": {
      "create_classroom": true,
      "create_exam": true,
      "ai_question_generation": true,
      "advanced_analytics": true,
      "bulk_operations": true,
      "priority_support": true
    },
    "is_active": true,
    "plan_type": "premium",
    "target_user_type": "teacher",
    "max_classrooms": 20,
    "max_students_per_classroom": 100,
    "max_exams_per_month": 50,
    "max_questions_per_exam": 200,
    "allows_home_tutoring": false,
    "allows_ai_question_generation": true,
    "allows_advanced_analytics": true,
    "priority_support": true,
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  },
  {
    "id": "550e8400-e29b-41d4-a716-446655440002",
    "name": "Teacher Home Tutor",
    "description": "Special plan for home tutoring services",
    "price": 4999,
    "duration_days": 30,
    "features": {
      "create_classroom": true,
      "create_exam": true,
      "ai_question_generation": true,
      "advanced_analytics": true,
      "home_tutoring": true,
      "bulk_operations": true,
      "priority_support": true,
      "payment_processing": true
    },
    "is_active": true,
    "plan_type": "home_tutor",
    "target_user_type": "teacher",
    "max_classrooms": 50,
    "max_students_per_classroom": 200,
    "max_exams_per_month": 100,
    "max_questions_per_exam": 500,
    "allows_home_tutoring": true,
    "allows_ai_question_generation": true,
    "allows_advanced_analytics": true,
    "priority_support": true,
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  }
]
```

### **Get My Subscription**
```http
GET /api/subscriptions/my-subscription
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Response (200):**
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440010",
  "user_id": "123e4567-e89b-12d3-a456-************",
  "plan_id": "550e8400-e29b-41d4-a716-446655440001",
  "start_date": "2024-01-15T10:30:00Z",
  "end_date": "2024-02-15T10:30:00Z",
  "trial_end_date": null,
  "next_billing_date": "2024-02-15T10:30:00Z",
  "status": "active",
  "auto_renew": true,
  "billing_cycle": "monthly",
  "is_trial": false,
  "payment_reference": "dummy_payment_ref_123",
  "current_usage": {
    "classrooms_created": 5,
    "students_enrolled": 150,
    "exams_created": 12,
    "questions_generated": 45,
    "ai_questions_used": 23,
    "analytics_views": 8
  },
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z",
  "plan": {
    "id": "550e8400-e29b-41d4-a716-446655440001",
    "name": "Teacher Premium",
    "description": "Premium plan for teachers with advanced features",
    "price": 2999,
    "duration_days": 30,
    "plan_type": "premium",
    "target_user_type": "teacher",
    "max_classrooms": 20,
    "max_students_per_classroom": 100,
    "max_exams_per_month": 50,
    "allows_ai_question_generation": true,
    "allows_home_tutoring": false,
    "allows_advanced_analytics": true,
    "priority_support": true,
    "is_active": true,
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  }
}
```

**Response (404) - No Subscription:**
```json
null
```

### **Upgrade Subscription**
```http
POST /api/subscriptions/upgrade
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Request Body:**
```json
{
  "plan_id": "550e8400-e29b-41d4-a716-446655440002"
}
```

**Response (200):**
```json
{
  "message": "Subscription upgraded successfully",
  "subscription": {
    "id": "550e8400-e29b-41d4-a716-446655440010",
    "user_id": "123e4567-e89b-12d3-a456-************",
    "plan_id": "550e8400-e29b-41d4-a716-446655440002",
    "status": "active",
    "start_date": "2024-01-15T10:30:00Z",
    "end_date": "2024-02-15T10:30:00Z",
    "billing_cycle": "monthly",
    "auto_renew": true,
    "is_trial": false,
    "created_at": "2024-01-15T10:30:00Z",
    "updated_at": "2024-01-15T10:30:00Z"
  }
}
```

**Error Responses:**
```json
// 404 - Plan not found
{
  "detail": "Subscription plan not found"
}

// 400 - Plan not available for user type
{
  "detail": "Plan is not available for teacher users"
}
```

### **Get My Usage**
```http
GET /api/subscriptions/usage/my-usage
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Response (200):**
```json
{
  "user_id": "123e4567-e89b-12d3-a456-************",
  "plan_name": "Teacher Premium",
  "current_usage": {
    "classrooms_created": 5,
    "students_enrolled": 150,
    "exams_created": 12,
    "questions_generated": 45,
    "ai_questions_used": 23,
    "analytics_views": 8
  },
  "plan_limits": {
    "max_classrooms": 20,
    "max_students_per_classroom": 100,
    "max_exams_per_month": 50,
    "max_questions_per_exam": 200
  },
  "usage_percentage": {
    "max_classrooms": 25.0,
    "max_exams_per_month": 24.0,
    "max_questions_per_exam": 22.5
  },
  "is_over_limit": false,
  "warnings": [
    "Approaching limit for max_classrooms: 5/20 (25.0%)"
  ]
}
```

**Response (404) - No Subscription:**
```json
null
```

---

## 🏠 **Home Tutoring System**

### **Search Home Tutors**
```http
POST /api/subscriptions/home-tutors/search
```

**Request Body:**
```json
{
  "subject_ids": ["550e8400-e29b-41d4-a716-446655440100"],
  "latitude": 40.7128,
  "longitude": -74.0060,
  "radius_km": 10,
  "max_hourly_rate": 50.00,
  "min_rating": 4.0,
  "available_days": ["monday", "wednesday", "friday"]
}
```

**Response (200):**
```json
{
  "tutors": [
    {
      "teacher_id": "123e4567-e89b-12d3-a456-************",
      "username": "john_teacher",
      "full_name": "John Smith",
      "bio": "Experienced mathematics teacher with 10+ years of experience",
      "experience_years": 10,
      "rating": 4.8,
      "hourly_rate_home": 45.00,
      "hourly_rate_online": 35.00,
      "subjects": [
        {
          "subject_id": "550e8400-e29b-41d4-a716-446655440100",
          "subject_name": "Mathematics"
        }
      ],
      "distance_km": 2.5,
      "available_days": ["monday", "wednesday", "friday", "saturday"],
      "preferred_contact_method": "whatsapp",
      "whatsapp_number": "+1234567890"
    }
  ],
  "total": 1,
  "search_radius_km": 10,
  "center_latitude": 40.7128,
  "center_longitude": -74.0060
}
```

### **Get Home Tutors (Query Parameters)**
```http
GET /api/subscriptions/home-tutors
```

**Query Parameters:**
- `subject_ids` (optional): Array of subject UUIDs
- `latitude` (optional): Search center latitude
- `longitude` (optional): Search center longitude
- `radius_km` (optional): Search radius in kilometers (default: 10)
- `max_hourly_rate` (optional): Maximum hourly rate filter
- `min_rating` (optional): Minimum rating filter
- `skip` (optional): Pagination offset (default: 0)
- `limit` (optional): Items per page (default: 20)

**Example Request:**
```http
GET /api/subscriptions/home-tutors?latitude=40.7128&longitude=-74.0060&radius_km=15&max_hourly_rate=60&min_rating=4.0&skip=0&limit=10
```

**Response:** Same as POST search endpoint

---

## 👨‍🏫 **Teacher Profile Management**

### **Update Teacher Profile**
```http
PUT /api/subscriptions/teacher/profile
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Request Body:**
```json
{
  "bio": "Experienced mathematics teacher specializing in advanced calculus",
  "experience_years": 12,
  "specialization": "Advanced Mathematics",
  "website": "https://johnsmith-math.com",
  "office_hours": "Monday-Friday 9AM-5PM",
  "offers_home_tutoring": true,
  "home_address": "123 Main St, New York, NY 10001",
  "latitude": 40.7128,
  "longitude": -74.0060,
  "tutoring_radius_km": 15,
  "hourly_rate_home": 50.00,
  "hourly_rate_online": 40.00,
  "preferred_contact_method": "whatsapp",
  "whatsapp_number": "+1234567890",
  "available_days": ["monday", "tuesday", "wednesday", "thursday", "friday"],
  "available_hours": {
    "monday": ["09:00", "17:00"],
    "tuesday": ["09:00", "17:00"],
    "wednesday": ["09:00", "17:00"],
    "thursday": ["09:00", "17:00"],
    "friday": ["09:00", "15:00"]
  },
  "subject_ids": [
    "550e8400-e29b-41d4-a716-446655440100",
    "550e8400-e29b-41d4-a716-446655440101"
  ]
}
```

**Response (200):**
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440200",
  "teacher_id": "123e4567-e89b-12d3-a456-************",
  "bio": "Experienced mathematics teacher specializing in advanced calculus",
  "experience_years": 12,
  "specialization": "Advanced Mathematics",
  "website": "https://johnsmith-math.com",
  "office_hours": "Monday-Friday 9AM-5PM",
  "rating": 4.8,
  "offers_home_tutoring": true,
  "home_address": "123 Main St, New York, NY 10001",
  "latitude": 40.7128,
  "longitude": -74.0060,
  "tutoring_radius_km": 15,
  "hourly_rate_home": 50.00,
  "hourly_rate_online": 40.00,
  "preferred_contact_method": "whatsapp",
  "whatsapp_number": "+1234567890",
  "available_days": ["monday", "tuesday", "wednesday", "thursday", "friday"],
  "available_hours": {
    "monday": ["09:00", "17:00"],
    "tuesday": ["09:00", "17:00"],
    "wednesday": ["09:00", "17:00"],
    "thursday": ["09:00", "17:00"],
    "friday": ["09:00", "15:00"]
  },
  "subjects": [
    {
      "subject_id": "550e8400-e29b-41d4-a716-446655440100",
      "subject_name": "Mathematics"
    },
    {
      "subject_id": "550e8400-e29b-41d4-a716-446655440101",
      "subject_name": "Physics"
    }
  ],
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T11:45:00Z"
}
```

### **Get My Teacher Profile**
```http
GET /api/subscriptions/teacher/profile
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Response (200):** Same as update response

### **Enable Home Tutoring**
```http
POST /api/subscriptions/teacher/enable-home-tutoring
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Response (200):**
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440200",
  "teacher_id": "123e4567-e89b-12d3-a456-************",
  "offers_home_tutoring": true,
  "bio": "Experienced mathematics teacher",
  "experience_years": 12,
  "rating": 4.8,
  "subjects": [
    {
      "subject_id": "550e8400-e29b-41d4-a716-446655440100",
      "subject_name": "Mathematics"
    }
  ],
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T11:45:00Z"
}
```

**Error Responses:**
```json
// 400 - Subscription doesn't allow home tutoring
{
  "detail": "Current subscription plan does not allow home tutoring. Please upgrade to a Home Tutor plan."
}

// 400 - No active subscription
{
  "detail": "No active subscription found"
}
```

---

## 📊 **Subscription Analytics (Admin Only)**

### **Get Subscription Statistics**
```http
GET /api/subscriptions/analytics/statistics
```

**Headers:**
```http
Authorization: Bearer <admin_token>
```

**Response (200):**
```json
{
  "total_subscriptions": 1250,
  "active_subscriptions": 980,
  "trial_subscriptions": 45,
  "expired_subscriptions": 225,
  "revenue_this_month": 29850.00,
  "revenue_total": 245670.00,
  "subscriptions_by_plan": {
    "Teacher Basic": 450,
    "Teacher Premium": 320,
    "Teacher Home Tutor": 85,
    "Student Basic": 395
  },
  "subscriptions_by_user_type": {
    "teacher": 855,
    "student": 395
  }
}
```

---

## ⚠️ **Error Handling**

### **Subscription-Related Errors**

#### **Feature Not Available (403)**
```json
{
  "detail": "AI Question Generation is not available in your Teacher Basic. Please upgrade to one of: Teacher Premium, Teacher Home Tutor"
}
```

#### **Usage Limit Exceeded (403)**
```json
{
  "detail": "You have reached your Classroom limit (2/2). Please upgrade your subscription to continue."
}
```

#### **Subscription Expired (403)**
```json
{
  "detail": "Subscription expired. Please renew your subscription to continue using this feature."
}
```

#### **Plan Not Available (400)**
```json
{
  "detail": "Plan is not available for teacher users"
}
```

---

## � **Usage Tracking**

### **Update Usage Metrics**
```http
POST /api/subscriptions/usage/update
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Query Parameters:**
- `metric_name` (required): Name of the metric to update
- `increment` (optional): Amount to increment (default: 1)

**Example Request:**
```http
POST /api/subscriptions/usage/update?metric_name=classrooms_created&increment=1
```

**Response (200):**
```json
{
  "message": "Usage updated successfully"
}
```

**Error Response (404):**
```json
{
  "detail": "User subscription not found"
}
```

---

## 📋 **Available Metrics for Usage Tracking**

- `classrooms_created`
- `students_enrolled`
- `exams_created`
- `questions_generated`
- `ai_questions_used`
- `analytics_views`
- `competitions_created`

---

## 🎯 **Frontend Integration Notes**

### **Subscription Checks**
- Always check user subscription status before showing premium features
- Display upgrade prompts when users hit limits
- Show usage progress bars for limited features

### **Error Handling**
- Handle 403 errors gracefully with upgrade suggestions
- Show clear error messages for subscription-related issues
- Provide direct links to upgrade pages

### **UI Recommendations**
- Badge indicators for plan types (Basic, Premium, Home Tutor)
- Usage meters for limited features
- Clear feature comparison tables
- Prominent upgrade buttons for limited users

### **Auto-Assignment**
- New users automatically get basic plans
- No manual subscription setup required
- Basic features work immediately after registration

---

## 🎪 **Events System**

### **Get Public Events**
```http
GET /api/events/public
```

**Query Parameters:**
- `category_id` (optional): Filter by category UUID
- `location_id` (optional): Filter by location UUID
- `is_featured` (optional): Filter featured events (`true`/`false`)
- `is_competition` (optional): Filter competition events (`true`/`false`)
- `search` (optional): Search term
- `page` (optional): Page number (default: 1)
- `size` (optional): Page size (default: 20, max: 100)

**Example Request:**
```http
GET /api/events/public?is_featured=true&page=1&size=10
```

**Response (200):**
```json
{
  "events": [
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "title": "Mathematics Competition 2024",
      "description": "Annual mathematics competition for high school students",
      "short_description": "Test your math skills in our annual competition",
      "start_datetime": "2024-03-15T09:00:00Z",
      "end_datetime": "2024-03-15T17:00:00Z",
      "registration_start": "2024-02-01T00:00:00Z",
      "registration_end": "2024-03-10T23:59:59Z",
      "is_featured": true,
      "is_public": true,
      "is_competition": true,
      "max_attendees": 500,
      "current_attendees": 245,
      "banner_image_url": "https://storage.example.com/events/math-comp-2024.jpg",
      "organizer_id": "123e4567-e89b-12d3-a456-************",
      "category": {
        "id": "550e8400-e29b-41d4-a716-************",
        "name": "Academic Competition",
        "description": "Educational competitions and contests"
      },
      "location": {
        "id": "550e8400-e29b-41d4-a716-************",
        "name": "Central High School",
        "address": "123 Education St, City, State 12345",
        "latitude": 40.7128,
        "longitude": -74.0060
      },
      "created_at": "2024-01-15T10:30:00Z",
      "updated_at": "2024-01-15T10:30:00Z"
    }
  ],
  "total": 1,
  "page": 1,
  "size": 10,
  "total_pages": 1
}
```

### **Get Public Event Details**
```http
GET /api/events/public/{event_id}
```

**Response (200):**
```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "title": "Mathematics Competition 2024",
  "description": "Annual mathematics competition for high school students. This comprehensive competition covers algebra, geometry, calculus, and statistics.",
  "short_description": "Test your math skills in our annual competition",
  "start_datetime": "2024-03-15T09:00:00Z",
  "end_datetime": "2024-03-15T17:00:00Z",
  "registration_start": "2024-02-01T00:00:00Z",
  "registration_end": "2024-03-10T23:59:59Z",
  "is_featured": true,
  "is_public": true,
  "is_competition": true,
  "requires_approval": false,
  "max_attendees": 500,
  "min_attendees": 50,
  "current_attendees": 245,
  "banner_image_url": "https://storage.example.com/events/math-comp-2024.jpg",
  "organizer_id": "123e4567-e89b-12d3-a456-************",
  "status": "upcoming",
  "competition_rules": "Standard competition rules apply. No calculators allowed.",
  "prize_details": {
    "first_place": "$1000 scholarship",
    "second_place": "$500 scholarship",
    "third_place": "$250 scholarship"
  },
  "category": {
    "id": "550e8400-e29b-41d4-a716-************",
    "name": "Academic Competition",
    "description": "Educational competitions and contests",
    "color": "#FF6B6B"
  },
  "location": {
    "id": "550e8400-e29b-41d4-a716-************",
    "name": "Central High School",
    "address": "123 Education St, City, State 12345",
    "latitude": 40.7128,
    "longitude": -74.0060,
    "capacity": 1000,
    "facilities": ["Auditorium", "Computer Lab", "Parking"]
  },
  "speakers": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440600",
      "name": "Dr. Jane Smith",
      "title": "Mathematics Professor",
      "bio": "Leading expert in advanced mathematics with 20+ years experience",
      "profile_image_url": "https://storage.example.com/speakers/jane-smith.jpg",
      "is_featured": true
    }
  ],
  "tickets": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440700",
      "name": "Student Ticket",
      "description": "For high school students",
      "price": 0.00,
      "quantity_available": 400,
      "quantity_sold": 245,
      "is_active": true
    }
  ],
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

### **Get Featured Events**
```http
GET /api/events/featured?limit=5
```

**Query Parameters:**
- `limit` (optional): Number of events (default: 5, max: 20)

**Response (200):**
```json
[
  {
    "id": "550e8400-e29b-41d4-a716-************",
    "title": "Mathematics Competition 2024",
    "short_description": "Test your math skills in our annual competition",
    "start_datetime": "2024-03-15T09:00:00Z",
    "banner_image_url": "https://storage.example.com/events/math-comp-2024.jpg",
    "is_competition": true,
    "current_attendees": 245,
    "max_attendees": 500
  }
]
```

### **Search Events**
```http
GET /api/events/search?q=mathematics&limit=20
```

**Query Parameters:**
- `q` (required): Search term
- `limit` (optional): Number of results (default: 20, max: 100)

**Response (200):** Same format as featured events

### **Create Event (Teachers Only)**
```http
POST /api/events/
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Request Body:**
```json
{
  "title": "Science Fair 2024",
  "description": "Annual science fair showcasing student projects",
  "short_description": "Showcase your science projects",
  "start_datetime": "2024-04-20T09:00:00Z",
  "end_datetime": "2024-04-20T17:00:00Z",
  "registration_start": "2024-03-01T00:00:00Z",
  "registration_end": "2024-04-15T23:59:59Z",
  "category_id": "550e8400-e29b-41d4-a716-************",
  "location_id": "550e8400-e29b-41d4-a716-************",
  "is_public": true,
  "is_featured": false,
  "is_competition": false,
  "requires_approval": false,
  "max_attendees": 200,
  "min_attendees": 20,
  "banner_image_url": "https://storage.example.com/events/science-fair-2024.jpg"
}
```

**Response (201):**
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440301",
  "title": "Science Fair 2024",
  "description": "Annual science fair showcasing student projects",
  "short_description": "Showcase your science projects",
  "start_datetime": "2024-04-20T09:00:00Z",
  "end_datetime": "2024-04-20T17:00:00Z",
  "registration_start": "2024-03-01T00:00:00Z",
  "registration_end": "2024-04-15T23:59:59Z",
  "organizer_id": "123e4567-e89b-12d3-a456-************",
  "category_id": "550e8400-e29b-41d4-a716-************",
  "location_id": "550e8400-e29b-41d4-a716-************",
  "is_public": true,
  "is_featured": false,
  "is_competition": false,
  "requires_approval": false,
  "max_attendees": 200,
  "min_attendees": 20,
  "current_attendees": 0,
  "status": "upcoming",
  "banner_image_url": "https://storage.example.com/events/science-fair-2024.jpg",
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

### **Get My Events (Teachers Only)**
```http
GET /api/events/my-events?page=1&size=10
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `size` (optional): Page size (default: 20, max: 100)

**Response (200):** Same format as public events list

### **Update Event (Organizer Only)**
```http
PUT /api/events/{event_id}
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Request Body:**
```json
{
  "title": "Updated Science Fair 2024",
  "description": "Updated description for the science fair",
  "max_attendees": 250,
  "is_featured": true
}
```

**Response (200):** Same format as create event response

---

## 🎫 **Event Registration**

### **Register for Event**
```http
POST /api/events/{event_id}/register
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Request Body:**
```json
{
  "ticket_id": "550e8400-e29b-41d4-a716-446655440700",
  "additional_info": {
    "dietary_requirements": "Vegetarian",
    "emergency_contact": "+1234567890"
  }
}
```

**Response (201):**
```json
{
  "id": "550e8400-e29b-41d4-a716-446655440800",
  "event_id": "550e8400-e29b-41d4-a716-************",
  "user_id": "123e4567-e89b-12d3-a456-************",
  "ticket_id": "550e8400-e29b-41d4-a716-446655440700",
  "registration_date": "2024-01-15T10:30:00Z",
  "status": "confirmed",
  "additional_info": {
    "dietary_requirements": "Vegetarian",
    "emergency_contact": "+1234567890"
  },
  "event": {
    "id": "550e8400-e29b-41d4-a716-************",
    "title": "Mathematics Competition 2024",
    "start_datetime": "2024-03-15T09:00:00Z"
  },
  "ticket": {
    "id": "550e8400-e29b-41d4-a716-446655440700",
    "name": "Student Ticket",
    "price": 0.00
  }
}
```

### **Get My Registrations**
```http
GET /api/events/my-registrations?page=1&size=10
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `size` (optional): Page size (default: 20, max: 100)

**Response (200):**
```json
{
  "registrations": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440800",
      "event_id": "550e8400-e29b-41d4-a716-************",
      "registration_date": "2024-01-15T10:30:00Z",
      "status": "confirmed",
      "event": {
        "id": "550e8400-e29b-41d4-a716-************",
        "title": "Mathematics Competition 2024",
        "start_datetime": "2024-03-15T09:00:00Z",
        "banner_image_url": "https://storage.example.com/events/math-comp-2024.jpg"
      },
      "ticket": {
        "name": "Student Ticket",
        "price": 0.00
      }
    }
  ],
  "total": 1,
  "page": 1,
  "size": 10,
  "total_pages": 1
}
```

### **Cancel Registration**
```http
DELETE /api/events/{event_id}/register
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Response (200):**
```json
{
  "message": "Registration cancelled successfully"
}
```

---

## 🏆 **Competitions System**

### **Create Competition from Exam**
```http
POST /api/competitions/from-exam/{exam_id}
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Query Parameters:**
- `title` (required): Competition title
- `start_datetime` (required): Start date and time (ISO format)
- `end_datetime` (required): End date and time (ISO format)
- `category_id` (required): Event category UUID
- `location_id` (optional): Event location UUID
- `institute_id` (optional): Institute UUID
- `max_participants` (optional): Maximum participants (default: 100)
- `registration_fee` (optional): Registration fee (default: 0.0)
- `description` (optional): Competition description
- `is_public` (optional): Public visibility (default: true)

**Example Request:**
```http
POST /api/competitions/from-exam/550e8400-e29b-41d4-a716-************?title=Math%20Championship&start_datetime=2024-03-15T09:00:00Z&end_datetime=2024-03-15T17:00:00Z&category_id=550e8400-e29b-41d4-a716-************
```

**Response (201):**
```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "title": "Math Championship",
  "description": "Competition based on advanced mathematics exam",
  "start_datetime": "2024-03-15T09:00:00Z",
  "end_datetime": "2024-03-15T17:00:00Z",
  "organizer_id": "123e4567-e89b-12d3-a456-************",
  "category_id": "550e8400-e29b-41d4-a716-************",
  "location_id": "550e8400-e29b-41d4-a716-************",
  "is_competition": true,
  "is_public": true,
  "max_attendees": 100,
  "current_attendees": 0,
  "registration_fee": 0.0,
  "status": "upcoming",
  "exam_id": "550e8400-e29b-41d4-a716-************",
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

### **Get All Competitions**
```http
GET /api/competitions
```

**Query Parameters:**
- `skip` (optional): Pagination offset (default: 0)
- `limit` (optional): Items per page (default: 20, max: 100)
- `category_id` (optional): Filter by category UUID
- `status` (optional): Filter by status (`upcoming`, `ongoing`, `completed`)

**Response (200):**
```json
[
  {
    "id": "550e8400-e29b-41d4-a716-************",
    "title": "Math Championship",
    "description": "Competition based on advanced mathematics exam",
    "start_datetime": "2024-03-15T09:00:00Z",
    "end_datetime": "2024-03-15T17:00:00Z",
    "status": "upcoming",
    "max_attendees": 100,
    "current_attendees": 45,
    "registration_fee": 0.0,
    "banner_image_url": "https://storage.example.com/competitions/math-champ.jpg",
    "category": {
      "id": "550e8400-e29b-41d4-a716-************",
      "name": "Academic Competition"
    },
    "location": {
      "id": "550e8400-e29b-41d4-a716-************",
      "name": "Central High School",
      "address": "123 Education St, City, State 12345"
    }
  }
]
```

---

## 👨‍🎓 **Mentors System**

### **Register as Mentor**
```http
POST /api/mentors/register
```

**Request Body:**
```json
{
  "username": "mentor_john",
  "email": "<EMAIL>",
  "mobile": "+1234567890",
  "password": "securepassword123",
  "country": "United States",
  "full_name": "John Mentor Smith",
  "bio": "Experienced educator with 15+ years in mathematics and science",
  "expertise_areas": ["Mathematics", "Physics", "Computer Science"],
  "experience_years": 15,
  "education_background": "PhD in Mathematics from MIT",
  "certifications": ["Certified Math Teacher", "Science Education Specialist"],
  "hourly_rate": 75.00,
  "availability": {
    "monday": ["09:00", "17:00"],
    "tuesday": ["09:00", "17:00"],
    "wednesday": ["09:00", "17:00"],
    "thursday": ["09:00", "17:00"],
    "friday": ["09:00", "15:00"]
  },
  "preferred_subjects": ["Advanced Mathematics", "Calculus", "Physics"],
  "languages": ["English", "Spanish"]
}
```

**Response (201):**
```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "username": "mentor_john",
  "email": "<EMAIL>",
  "mobile": "+1234567890",
  "user_type": "mentor",
  "country": "United States",
  "is_email_verified": false,
  "is_mobile_verified": false,
  "mentor_profile": {
    "id": "550e8400-e29b-41d4-a716-446655441001",
    "full_name": "John Mentor Smith",
    "bio": "Experienced educator with 15+ years in mathematics and science",
    "expertise_areas": ["Mathematics", "Physics", "Computer Science"],
    "experience_years": 15,
    "education_background": "PhD in Mathematics from MIT",
    "certifications": ["Certified Math Teacher", "Science Education Specialist"],
    "hourly_rate": 75.00,
    "rating": 0.0,
    "total_reviews": 0,
    "is_verified": false,
    "is_available": true,
    "preferred_subjects": ["Advanced Mathematics", "Calculus", "Physics"],
    "languages": ["English", "Spanish"]
  },
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

### **Get Public Mentors**
```http
GET /api/mentors/public
```

**Query Parameters:**
- `search` (optional): Search term for name or expertise
- `expertise_areas` (optional): Array of expertise areas
- `experience_years_min` (optional): Minimum years of experience
- `experience_years_max` (optional): Maximum years of experience
- `hourly_rate_min` (optional): Minimum hourly rate
- `hourly_rate_max` (optional): Maximum hourly rate
- `rating_min` (optional): Minimum rating (0-5)
- `country` (optional): Filter by country
- `verified_only` (optional): Show only verified mentors (default: true)
- `page` (optional): Page number (default: 1)
- `size` (optional): Page size (default: 20, max: 100)

**Example Request:**
```http
GET /api/mentors/public?expertise_areas=Mathematics&experience_years_min=5&rating_min=4.0&page=1&size=10
```

**Response (200):**
```json
{
  "mentors": [
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "username": "mentor_john",
      "full_name": "John Mentor Smith",
      "bio": "Experienced educator with 15+ years in mathematics and science",
      "expertise_areas": ["Mathematics", "Physics", "Computer Science"],
      "experience_years": 15,
      "hourly_rate": 75.00,
      "rating": 4.8,
      "total_reviews": 127,
      "is_verified": true,
      "is_available": true,
      "country": "United States",
      "profile_image_url": "https://storage.example.com/mentors/john-smith.jpg",
      "preferred_subjects": ["Advanced Mathematics", "Calculus", "Physics"],
      "languages": ["English", "Spanish"]
    }
  ],
  "total": 1,
  "page": 1,
  "size": 10,
  "total_pages": 1
}
```

### **Get Mentor Details**
```http
GET /api/mentors/{mentor_id}
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Response (200):**
```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "username": "mentor_john",
  "email": "<EMAIL>",
  "full_name": "John Mentor Smith",
  "bio": "Experienced educator with 15+ years in mathematics and science",
  "expertise_areas": ["Mathematics", "Physics", "Computer Science"],
  "experience_years": 15,
  "education_background": "PhD in Mathematics from MIT",
  "certifications": ["Certified Math Teacher", "Science Education Specialist"],
  "hourly_rate": 75.00,
  "rating": 4.8,
  "total_reviews": 127,
  "is_verified": true,
  "is_available": true,
  "country": "United States",
  "profile_image_url": "https://storage.example.com/mentors/john-smith.jpg",
  "preferred_subjects": ["Advanced Mathematics", "Calculus", "Physics"],
  "languages": ["English", "Spanish"],
  "availability": {
    "monday": ["09:00", "17:00"],
    "tuesday": ["09:00", "17:00"],
    "wednesday": ["09:00", "17:00"],
    "thursday": ["09:00", "17:00"],
    "friday": ["09:00", "15:00"]
  },
  "portfolio_items": [
    {
      "title": "Advanced Calculus Course",
      "description": "Comprehensive calculus curriculum for university students",
      "url": "https://example.com/portfolio/calculus"
    }
  ],
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

### **Update Mentor Profile (Mentors Only)**
```http
PUT /api/mentors/profile
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Request Body:**
```json
{
  "bio": "Updated bio with new achievements",
  "expertise_areas": ["Mathematics", "Physics", "Computer Science", "Data Science"],
  "hourly_rate": 80.00,
  "availability": {
    "monday": ["10:00", "18:00"],
    "tuesday": ["10:00", "18:00"],
    "wednesday": ["10:00", "18:00"],
    "thursday": ["10:00", "18:00"],
    "friday": ["10:00", "16:00"]
  },
  "is_available": true
}
```

**Response (200):** Same format as get mentor details

---

## 🎯 **Mentor-Institute Association**

### **Apply to Institute (Mentors Only)**
```http
POST /api/mentors/apply-to-institute
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Request Body:**
```json
{
  "institute_id": "550e8400-e29b-41d4-a716-446655441100",
  "application_message": "I would like to join your institute as a mathematics mentor. I have 15+ years of experience in teaching advanced mathematics.",
  "proposed_hourly_rate": 75.00,
  "availability_hours_per_week": 20,
  "preferred_subjects": ["Advanced Mathematics", "Calculus", "Statistics"]
}
```

**Response (201):**
```json
{
  "id": "550e8400-e29b-41d4-a716-446655441200",
  "mentor_id": "550e8400-e29b-41d4-a716-************",
  "institute_id": "550e8400-e29b-41d4-a716-446655441100",
  "status": "pending",
  "application_type": "mentor_application",
  "application_message": "I would like to join your institute as a mathematics mentor. I have 15+ years of experience in teaching advanced mathematics.",
  "proposed_hourly_rate": 75.00,
  "availability_hours_per_week": 20,
  "preferred_subjects": ["Advanced Mathematics", "Calculus", "Statistics"],
  "applied_at": "2024-01-15T10:30:00Z",
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

### **Invite Mentor (Institutes Only)**
```http
POST /api/mentors/invite-mentor
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Request Body:**
```json
{
  "mentor_id": "550e8400-e29b-41d4-a716-************",
  "invitation_message": "We would like to invite you to join our institute as a senior mathematics mentor.",
  "offered_hourly_rate": 80.00,
  "expected_hours_per_week": 25,
  "subjects_to_cover": ["Advanced Mathematics", "Calculus", "Linear Algebra"]
}
```

**Response (201):** Same format as apply to institute

### **Respond to Association (Mentors/Institutes)**
```http
POST /api/mentors/associations/{association_id}/respond
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Request Body:**
```json
{
  "response": "accepted",
  "response_message": "I'm excited to join your institute and contribute to student success.",
  "final_hourly_rate": 78.00,
  "final_hours_per_week": 22
}
```

**Response (200):**
```json
{
  "id": "550e8400-e29b-41d4-a716-446655441200",
  "mentor_id": "550e8400-e29b-41d4-a716-************",
  "institute_id": "550e8400-e29b-41d4-a716-446655441100",
  "status": "active",
  "response": "accepted",
  "response_message": "I'm excited to join your institute and contribute to student success.",
  "final_hourly_rate": 78.00,
  "final_hours_per_week": 22,
  "start_date": "2024-01-15T10:30:00Z",
  "responded_at": "2024-01-15T10:30:00Z",
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

---

## 📝 **Competition Mentor Checking**

### **Assign Mentor to Competition (Organizers Only)**
```http
POST /api/competitions/{competition_id}/assign-mentor
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Query Parameters:**
- `mentor_id` (required): Mentor UUID
- `assignment_notes` (optional): Notes for the assignment
- `questions_assigned` (optional): Array of question UUIDs
- `participants_assigned` (optional): Array of participant UUIDs
- `estimated_hours` (optional): Estimated checking hours
- `hourly_rate` (optional): Hourly rate for checking

**Example Request:**
```http
POST /api/competitions/550e8400-e29b-41d4-a716-************/assign-mentor?mentor_id=550e8400-e29b-41d4-a716-************&estimated_hours=10&hourly_rate=50
```

**Response (201):**
```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "competition_id": "550e8400-e29b-41d4-a716-************",
  "mentor_id": "550e8400-e29b-41d4-a716-************",
  "assigned_by": "123e4567-e89b-12d3-a456-************",
  "status": "pending",
  "assignment_notes": "Please check mathematics questions for accuracy and provide detailed feedback",
  "estimated_hours": 10.0,
  "hourly_rate": 50.0,
  "questions_assigned": ["550e8400-e29b-41d4-a716-************"],
  "participants_assigned": [],
  "assigned_at": "2024-01-15T10:30:00Z",
  "created_at": "2024-01-15T10:30:00Z",
  "updated_at": "2024-01-15T10:30:00Z"
}
```

### **Get Mentor Assignments (Mentors Only)**
```http
GET /api/mentor/assignments
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Query Parameters:**
- `status` (optional): Filter by status (`pending`, `accepted`, `completed`)
- `skip` (optional): Pagination offset (default: 0)
- `limit` (optional): Items per page (default: 20, max: 100)

**Response (200):**
```json
{
  "assignments": [
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "competition_id": "550e8400-e29b-41d4-a716-************",
      "status": "accepted",
      "assignment_notes": "Please check mathematics questions for accuracy",
      "estimated_hours": 10.0,
      "hourly_rate": 50.0,
      "assigned_at": "2024-01-15T10:30:00Z",
      "competition": {
        "id": "550e8400-e29b-41d4-a716-************",
        "title": "Math Championship",
        "start_datetime": "2024-03-15T09:00:00Z",
        "end_datetime": "2024-03-15T17:00:00Z"
      }
    }
  ],
  "total": 1
}
```

### **Respond to Assignment (Mentors Only)**
```http
POST /api/mentor/assignments/{assignment_id}/respond
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Request Body:**
```json
{
  "response": "accepted",
  "response_notes": "I accept this assignment and will complete it by the deadline.",
  "estimated_completion_time": "2024-03-20T17:00:00Z"
}
```

**Response (200):**
```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "status": "accepted",
  "response": "accepted",
  "response_notes": "I accept this assignment and will complete it by the deadline.",
  "estimated_completion_time": "2024-03-20T17:00:00Z",
  "responded_at": "2024-01-15T11:00:00Z",
  "updated_at": "2024-01-15T11:00:00Z"
}
```

### **Get Answers to Check (Mentors Only)**
```http
GET /api/mentor/answers-to-check
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Query Parameters:**
- `competition_id` (optional): Filter by competition UUID
- `question_id` (optional): Filter by question UUID
- `skip` (optional): Pagination offset (default: 0)
- `limit` (optional): Items per page (default: 20, max: 100)

**Response (200):**
```json
{
  "answers": [
    {
      "id": "550e8400-e29b-41d4-a716-************",
      "question_id": "550e8400-e29b-41d4-a716-************",
      "participant_id": "550e8400-e29b-41d4-a716-************",
      "answer_text": "The derivative of x^2 is 2x",
      "submitted_at": "2024-03-15T10:30:00Z",
      "auto_score": 8.5,
      "mentor_score": null,
      "mentor_feedback": null,
      "question": {
        "id": "550e8400-e29b-41d4-a716-************",
        "question_text": "What is the derivative of x^2?",
        "max_marks": 10,
        "question_type": "short_answer"
      },
      "participant": {
        "id": "550e8400-e29b-41d4-a716-************",
        "username": "student_alice",
        "email": "<EMAIL>"
      }
    }
  ]
}
```

### **Submit Answer Score (Mentors Only)**
```http
POST /api/mentor/answers/{answer_id}/score
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Query Parameters:**
- `mentor_score` (required): Score given by mentor (0-10)
- `mentor_feedback` (optional): Feedback text

**Example Request:**
```http
POST /api/mentor/answers/550e8400-e29b-41d4-a716-************/score?mentor_score=9.0&mentor_feedback=Excellent%20answer%20with%20clear%20explanation
```

**Response (200):**
```json
{
  "id": "550e8400-e29b-41d4-a716-************",
  "mentor_score": 9.0,
  "mentor_feedback": "Excellent answer with clear explanation",
  "checked_at": "2024-01-15T12:00:00Z",
  "checked_by": "550e8400-e29b-41d4-a716-************",
  "final_score": 9.0,
  "updated_at": "2024-01-15T12:00:00Z"
}
```

### **Bulk Score Answers (Mentors Only)**
```http
POST /api/mentor/answers/bulk-score
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Request Body:**
```json
[
  {
    "answer_id": "550e8400-e29b-41d4-a716-************",
    "score": 9.0,
    "feedback": "Excellent answer"
  },
  {
    "answer_id": "550e8400-e29b-41d4-a716-************",
    "score": 7.5,
    "feedback": "Good answer, minor errors"
  }
]
```

**Response (200):**
```json
{
  "message": "Bulk scoring completed successfully",
  "processed": 2,
  "failed": 0,
  "results": [
    {
      "answer_id": "550e8400-e29b-41d4-a716-************",
      "status": "success",
      "final_score": 9.0
    },
    {
      "answer_id": "550e8400-e29b-41d4-a716-************",
      "status": "success",
      "final_score": 7.5
    }
  ]
}
```

### **Get Mentor Statistics (Mentors Only)**
```http
GET /api/mentor/statistics
```

**Headers:**
```http
Authorization: Bearer <token>
```

**Query Parameters:**
- `competition_id` (optional): Filter by competition UUID

**Response (200):**
```json
{
  "total_assignments": 15,
  "completed_assignments": 12,
  "pending_assignments": 2,
  "rejected_assignments": 1,
  "total_answers_checked": 450,
  "average_score_given": 7.8,
  "total_earnings": 2400.00,
  "average_rating": 4.7,
  "completion_rate": 0.8,
  "response_time_hours": 24.5,
  "monthly_stats": {
    "january": {
      "assignments_completed": 5,
      "answers_checked": 150,
      "earnings": 750.00
    }
  }
}
```

---

## ⚠️ **Events & Mentors Error Handling**

### **Event Registration Errors**

#### **Event Full (400)**
```json
{
  "detail": "Event has reached maximum capacity (500/500)"
}
```

#### **Registration Closed (400)**
```json
{
  "detail": "Registration period has ended for this event"
}
```

#### **Already Registered (400)**
```json
{
  "detail": "User is already registered for this event"
}
```

### **Competition Errors**

#### **Exam Not Found (404)**
```json
{
  "detail": "Exam not found or not accessible"
}
```

#### **Competition Creation Failed (400)**
```json
{
  "detail": "Cannot create competition: exam has no questions"
}
```

### **Mentor Assignment Errors**

#### **Mentor Not Available (400)**
```json
{
  "detail": "Mentor is not available for assignments"
}
```

#### **Already Assigned (400)**
```json
{
  "detail": "Mentor is already assigned to this competition"
}
```

#### **Invalid Score (400)**
```json
{
  "detail": "Score must be between 0 and 10"
}
```

---

## 🎯 **Frontend Integration Notes for Events & Mentors**

### **Event Management**
- Show event status badges (upcoming, ongoing, completed)
- Display registration countdown timers
- Handle capacity warnings and waitlists
- Show event location on maps

### **Competition Features**
- Real-time participant count updates
- Competition leaderboards
- Progress tracking for ongoing competitions
- Results and analytics dashboards

### **Mentor System**
- Mentor verification badges
- Rating and review displays
- Availability calendars
- Skill and expertise tags
- Portfolio showcases

### **Mentor Checking Interface**
- Answer queue management
- Bulk scoring interfaces
- Progress tracking dashboards
- Feedback and scoring forms
- Statistics and earnings tracking

### **Responsive Design Considerations**
- Mobile-friendly mentor checking interfaces
- Touch-optimized scoring controls
- Offline capability for answer checking
- Real-time notifications for new assignments
```
