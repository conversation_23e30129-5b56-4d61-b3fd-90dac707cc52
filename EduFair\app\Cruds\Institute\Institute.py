from typing import List, Optional
import uuid
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc
from fastapi import HTTPException
from datetime import datetime, timezone
import bcrypt

# Import Models
from Models.users import User, UserTypeEnum, InstituteProfile
from Models.Events import Event

# Import Schemas
from Schemas.Institute.Institute import (
    InstituteRegistrationBase, InstituteProfileUpdate, InstituteVerificationUpdate,
    InstituteUserOut, InstituteDetailedOut, InstituteListOut, InstituteListResponse,
    InstituteSearchFilter, InstituteStatsOut, InstituteVerificationListOut
)


def hash_password(password: str) -> str:
    """Hash password using bcrypt"""
    salt = bcrypt.gensalt()
    hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
    return hashed.decode('utf-8')


def register_institute(db: Session, institute_data: InstituteRegistrationBase) -> InstituteUserOut:
    """Register a new institute"""
    
    # Check if username already exists
    existing_user = db.query(User).filter(User.username == institute_data.username).first()
    if existing_user:
        raise HTTPException(status_code=400, detail="Username already exists")
    
    # Check if email already exists
    existing_email = db.query(User).filter(User.email == institute_data.email).first()
    if existing_email:
        raise HTTPException(status_code=400, detail="Email already exists")
    
    # Check if mobile already exists
    existing_mobile = db.query(User).filter(User.mobile == institute_data.mobile).first()
    if existing_mobile:
        raise HTTPException(status_code=400, detail="Mobile number already exists")
    
    # Create user account
    hashed_password = hash_password(institute_data.password)
    
    user = User(
        username=institute_data.username,
        email=institute_data.email,
        mobile=institute_data.mobile,
        password_hash=hashed_password,
        country=institute_data.country,
        user_type=UserTypeEnum.institute,
        is_email_verified=False,
        is_mobile_verified=False
    )
    
    db.add(user)
    db.flush()  # Get the user ID
    
    # Create institute profile
    institute_profile = InstituteProfile(
        user_id=user.id,
        institute_name=institute_data.institute_name,
        description=institute_data.description,
        address=institute_data.address,
        city=institute_data.city,
        state=institute_data.state,
        country=institute_data.country,
        postal_code=institute_data.postal_code,
        website=institute_data.website,
        phone=institute_data.phone,
        email=institute_data.institute_email,
        established_year=institute_data.established_year,
        institute_type=institute_data.institute_type.value if institute_data.institute_type else None,
        accreditation=institute_data.accreditation,
        linkedin_url=institute_data.linkedin_url,
        facebook_url=institute_data.facebook_url,
        twitter_url=institute_data.twitter_url,
        is_verified=False,
        verification_status="pending"
    )
    
    db.add(institute_profile)
    db.commit()
    db.refresh(user)
    
    return InstituteUserOut.model_validate(user)


def get_institute_by_id(db: Session, institute_id: uuid.UUID) -> InstituteDetailedOut:
    """Get institute by ID with detailed information"""
    
    user = db.query(User).options(
        joinedload(User.institute_profile)
    ).filter(
        User.id == institute_id,
        User.user_type == UserTypeEnum.institute
    ).first()
    
    if not user:
        raise HTTPException(status_code=404, detail="Institute not found")
    
    # Get statistics
    total_competitions = db.query(Event).filter(
        Event.institute_id == institute_id,
        Event.is_competition == True
    ).count()
    
    active_competitions = db.query(Event).filter(
        Event.institute_id == institute_id,
        Event.is_competition == True,
        Event.status.in_(["published", "ongoing"])
    ).count()
    
    # Get total mentors associated
    from Models.users import MentorInstituteAssociation
    total_mentors = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.institute_id == institute_id,
        MentorInstituteAssociation.status == "active"
    ).count()
    
    return InstituteDetailedOut(
        user=InstituteUserOut.model_validate(user),
        profile=user.institute_profile,
        total_competitions=total_competitions,
        total_mentors=total_mentors,
        active_competitions=active_competitions,
        verification_status=user.institute_profile.verification_status
    )


def get_institutes(
    db: Session,
    filters: InstituteSearchFilter = None,
    skip: int = 0,
    limit: int = 20
) -> InstituteListResponse:
    """Get institutes with filtering and pagination"""
    
    query = db.query(User).options(
        joinedload(User.institute_profile)
    ).filter(User.user_type == UserTypeEnum.institute)
    
    # Apply filters
    if filters:
        if filters.search:
            search_term = f"%{filters.search}%"
            query = query.join(InstituteProfile).filter(
                or_(
                    User.username.ilike(search_term),
                    InstituteProfile.institute_name.ilike(search_term),
                    InstituteProfile.description.ilike(search_term)
                )
            )
        
        if filters.institute_type:
            query = query.join(InstituteProfile).filter(
                InstituteProfile.institute_type == filters.institute_type.value
            )
        
        if filters.country:
            query = query.filter(User.country == filters.country)
        
        if filters.state:
            query = query.join(InstituteProfile).filter(
                InstituteProfile.state == filters.state
            )
        
        if filters.city:
            query = query.join(InstituteProfile).filter(
                InstituteProfile.city == filters.city
            )
        
        if filters.verification_status:
            query = query.join(InstituteProfile).filter(
                InstituteProfile.verification_status == filters.verification_status.value
            )
        
        if filters.is_verified is not None:
            query = query.join(InstituteProfile).filter(
                InstituteProfile.is_verified == filters.is_verified
            )
        
        if filters.established_year_from:
            query = query.join(InstituteProfile).filter(
                InstituteProfile.established_year >= filters.established_year_from
            )
        
        if filters.established_year_to:
            query = query.join(InstituteProfile).filter(
                InstituteProfile.established_year <= filters.established_year_to
            )
    
    # Get total count
    total = query.count()
    
    # Apply pagination and ordering
    institutes = query.order_by(desc(User.created_at)).offset(skip).limit(limit).all()
    
    # Convert to response format
    institute_list = []
    for user in institutes:
        profile = user.institute_profile
        institute_list.append(InstituteListOut(
            id=user.id,
            username=user.username,
            institute_name=profile.institute_name,
            city=profile.city,
            state=profile.state,
            country=user.country,
            institute_type=profile.institute_type,
            is_verified=profile.is_verified,
            verification_status=profile.verification_status,
            logo_url=profile.logo_url,
            created_at=user.created_at
        ))
    
    return InstituteListResponse(
        institutes=institute_list,
        total=total,
        page=(skip // limit) + 1,
        size=limit,
        has_next=(skip + limit) < total,
        has_prev=skip > 0
    )


def update_institute_profile(
    db: Session,
    institute_id: uuid.UUID,
    profile_update: InstituteProfileUpdate
) -> InstituteDetailedOut:
    """Update institute profile"""
    
    user = db.query(User).options(
        joinedload(User.institute_profile)
    ).filter(
        User.id == institute_id,
        User.user_type == UserTypeEnum.institute
    ).first()
    
    if not user:
        raise HTTPException(status_code=404, detail="Institute not found")
    
    profile = user.institute_profile
    if not profile:
        raise HTTPException(status_code=404, detail="Institute profile not found")
    
    # Update profile fields
    update_data = profile_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        if hasattr(profile, field):
            setattr(profile, field, value)
    
    db.commit()
    db.refresh(user)
    
    return get_institute_by_id(db, institute_id)


def verify_institute(
    db: Session,
    institute_id: uuid.UUID,
    verification_update: InstituteVerificationUpdate,
    admin_id: uuid.UUID
) -> InstituteDetailedOut:
    """Verify or reject institute (admin only)"""
    
    user = db.query(User).options(
        joinedload(User.institute_profile)
    ).filter(
        User.id == institute_id,
        User.user_type == UserTypeEnum.institute
    ).first()
    
    if not user:
        raise HTTPException(status_code=404, detail="Institute not found")
    
    profile = user.institute_profile
    if not profile:
        raise HTTPException(status_code=404, detail="Institute profile not found")
    
    # Update verification status
    profile.verification_status = verification_update.verification_status.value
    profile.verification_notes = verification_update.verification_notes
    profile.verified_by = admin_id
    
    if verification_update.verification_status.value == "approved":
        profile.is_verified = True
        profile.verified_at = datetime.now(timezone.utc)
    else:
        profile.is_verified = False
        profile.verified_at = None
    
    db.commit()
    db.refresh(user)
    
    return get_institute_by_id(db, institute_id)


def get_institutes_pending_verification(
    db: Session,
    skip: int = 0,
    limit: int = 20
) -> List[InstituteVerificationListOut]:
    """Get institutes pending verification (admin only)"""
    
    institutes = db.query(User).options(
        joinedload(User.institute_profile)
    ).filter(
        User.user_type == UserTypeEnum.institute
    ).join(InstituteProfile).filter(
        InstituteProfile.verification_status == "pending"
    ).order_by(desc(User.created_at)).offset(skip).limit(limit).all()
    
    verification_list = []
    for user in institutes:
        profile = user.institute_profile
        verification_list.append(InstituteVerificationListOut(
            id=user.id,
            username=user.username,
            institute_name=profile.institute_name,
            institute_type=profile.institute_type,
            country=user.country,
            verification_status=profile.verification_status,
            created_at=user.created_at,
            submitted_documents=[]  # TODO: Implement document system
        ))
    
    return verification_list


def get_institute_statistics(db: Session) -> InstituteStatsOut:
    """Get institute statistics (admin only)"""
    
    # Total institutes
    total_institutes = db.query(User).filter(
        User.user_type == UserTypeEnum.institute
    ).count()
    
    # Verified institutes
    verified_institutes = db.query(User).join(InstituteProfile).filter(
        User.user_type == UserTypeEnum.institute,
        InstituteProfile.is_verified == True
    ).count()
    
    # Pending verification
    pending_verification = db.query(User).join(InstituteProfile).filter(
        User.user_type == UserTypeEnum.institute,
        InstituteProfile.verification_status == "pending"
    ).count()
    
    # Rejected institutes
    rejected_institutes = db.query(User).join(InstituteProfile).filter(
        User.user_type == UserTypeEnum.institute,
        InstituteProfile.verification_status == "rejected"
    ).count()
    
    # Institutes by type
    institutes_by_type = {}
    type_counts = db.query(
        InstituteProfile.institute_type,
        func.count(InstituteProfile.id)
    ).join(User).filter(
        User.user_type == UserTypeEnum.institute
    ).group_by(InstituteProfile.institute_type).all()
    
    for institute_type, count in type_counts:
        institutes_by_type[institute_type or "unknown"] = count
    
    # Institutes by country
    institutes_by_country = {}
    country_counts = db.query(
        User.country,
        func.count(User.id)
    ).filter(
        User.user_type == UserTypeEnum.institute
    ).group_by(User.country).all()
    
    for country, count in country_counts:
        institutes_by_country[country or "unknown"] = count
    
    # Recent registrations (last 30 days)
    from datetime import timedelta
    thirty_days_ago = datetime.now(timezone.utc) - timedelta(days=30)
    recent_registrations = db.query(User).filter(
        User.user_type == UserTypeEnum.institute,
        User.created_at >= thirty_days_ago
    ).count()
    
    return InstituteStatsOut(
        total_institutes=total_institutes,
        verified_institutes=verified_institutes,
        pending_verification=pending_verification,
        rejected_institutes=rejected_institutes,
        institutes_by_type=institutes_by_type,
        institutes_by_country=institutes_by_country,
        recent_registrations=recent_registrations
    )


def delete_institute(db: Session, institute_id: uuid.UUID, admin_id: uuid.UUID) -> bool:
    """Delete institute (admin only)"""

    user = db.query(User).filter(
        User.id == institute_id,
        User.user_type == UserTypeEnum.institute
    ).first()

    if not user:
        raise HTTPException(status_code=404, detail="Institute not found")

    # Check if institute has active competitions
    active_competitions = db.query(Event).filter(
        Event.institute_id == institute_id,
        Event.is_competition == True,
        Event.status.in_(["published", "ongoing"])
    ).count()

    if active_competitions > 0:
        raise HTTPException(
            status_code=400,
            detail="Cannot delete institute with active competitions"
        )

    db.delete(user)
    db.commit()
    return True


