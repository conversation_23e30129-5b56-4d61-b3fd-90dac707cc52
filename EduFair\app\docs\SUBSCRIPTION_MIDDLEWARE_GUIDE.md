# Subscription Middleware Implementation Guide

## 🎯 **Overview**

This guide shows how to implement subscription-based access control throughout the EduFair application using the new subscription middleware system.

## 🔧 **Hardcoded Subscription Plans**

All subscription plans are now hardcoded in `app/config/subscription_plans.py`:

### **Student Plans**
- **Basic (Free)**: 1-year access to basic features

### **Teacher Plans**
- **Basic (Free Trial)**: 1-month trial with limited features
  - 2 classrooms, 30 students each
  - 10 exams/month, 50 questions each
- **Premium ($29.99/month)**: Advanced features
  - 20 classrooms, 100 students each
  - 50 exams/month, 200 questions each
  - AI question generation
- **Home Tutor ($49.99/month)**: All premium + home tutoring
  - 50 classrooms, 200 students each
  - 100 exams/month, 500 questions each
  - Home tutoring features

### **Institute Plans**
- **Basic (Free Trial)**: 1-month trial
  - 10 teachers, 500 students
  - 20 classrooms, 5 competitions/month
- **Premium ($99.99/month)**: Full features
  - 100 teachers, 5000 students
  - 200 classrooms, 50 competitions/month
  - Mentor management, API access

### **Mentor Plans**
- **Basic (Free)**: Basic mentoring features
- **Premium ($19.99/month)**: Priority assignments, advanced analytics

## 🛡️ **Subscription Middleware Functions**

### **Feature-Based Permissions**
```python
from config.subscription_permission import (
    require_classroom_creation,
    require_exam_creation,
    require_competition_creation,
    require_ai_features,
    require_home_tutoring,
    require_advanced_analytics,
    require_mentor_management
)
```

### **Plan-Based Permissions**
```python
from config.subscription_permission import (
    require_premium_plan,
    require_teacher_or_institute,
    require_subscription_plan
)
```

## 📝 **Implementation Examples**

### **1. Classroom Creation (Already Implemented)**
```python
@router.post("/create", response_model=ClassroomOut)
def Create_Classroom_Endpoint(
    classroom: ClassroomCreate,
    db: Session = Depends(get_db),
    subscription_check = Depends(require_classroom_creation())
):
    current_user = subscription_check["user"]
    
    # Create classroom
    new_classroom = create_classroom(db, classroom, UUID(str(current_user.id)))
    
    # Update usage counter
    update_usage_counter(db, current_user, "classrooms_created", 1)
    
    return new_classroom
```

### **2. Exam Creation (Already Implemented)**
```python
@router.post("/create-with-assignment", response_model=ExamAssignmentOut)
def create_exam_with_assignment_endpoint(
    exam_in: ExamCreateWithAssignment,
    db: Session = Depends(get_db),
    subscription_check = Depends(require_exam_creation())
):
    current_user = subscription_check["user"]
    
    # Create exam with assignment
    new_exam = create_exam_with_assignment(db, exam_in, current_user)
    
    # Update usage counter
    update_usage_counter(db, current_user, "exams_created", 1)
    
    return new_exam
```

### **3. AI Question Generation**
```python
@router.post("/questions/generate-ai")
def generate_ai_questions(
    subject_id: UUID,
    chapter_id: UUID,
    count: int = 10,
    db: Session = Depends(get_db),
    subscription_check = Depends(require_ai_features())
):
    current_user = subscription_check["user"]
    
    # Generate AI questions
    questions = generate_questions_with_ai(db, subject_id, chapter_id, count)
    
    # Update usage counter
    update_usage_counter(db, current_user, "ai_questions_per_month", count)
    
    return questions
```

### **4. Home Tutoring Features**
```python
@router.post("/teacher/enable-home-tutoring")
def enable_home_tutoring(
    db: Session = Depends(get_db),
    subscription_check = Depends(require_home_tutoring())
):
    current_user = subscription_check["user"]
    
    # Enable home tutoring in teacher profile
    profile_update = TeacherProfileUpdate(offers_home_tutoring=True)
    return update_teacher_profile_with_subjects(db, current_user.id, profile_update)
```

### **5. Advanced Analytics**
```python
@router.get("/analytics/advanced")
def get_advanced_analytics(
    db: Session = Depends(get_db),
    subscription_check = Depends(require_advanced_analytics())
):
    current_user = subscription_check["user"]
    
    # Return advanced analytics
    return get_comprehensive_analytics(db, current_user.id)
```

### **6. Mentor Management (Institute Only)**
```python
@router.post("/competitions/{event_id}/assign-mentor")
def assign_mentor_to_competition(
    event_id: UUID,
    mentor_id: UUID,
    db: Session = Depends(get_db),
    subscription_check = Depends(require_mentor_management())
):
    current_user = subscription_check["user"]
    
    # Assign mentor
    return assign_mentor(db, event_id, mentor_id, current_user.id)
```

## 🚫 **Routes That Should NOT Have Subscription Checks**

### **Authentication & Registration**
- `POST /api/users/register`
- `POST /api/users/login`
- `POST /api/users/refresh-token`
- `POST /api/users/forgot-password`
- `POST /api/users/reset-password`

### **Subscription Management**
- `GET /api/subscriptions/plans`
- `POST /api/subscriptions/upgrade`
- `GET /api/subscriptions/my-subscription`
- All subscription-related endpoints

### **Public Endpoints**
- `GET /api/competitions/public`
- `GET /api/events/public`
- Health check endpoints

## 📊 **Usage Tracking**

### **Automatic Usage Counters**
The middleware automatically tracks:
- `classrooms_created`
- `exams_created`
- `competitions_created`
- `ai_questions_per_month`
- `analytics_views`

### **Manual Usage Updates**
```python
from config.subscription_permission import update_usage_counter

# Update usage manually
update_usage_counter(db, current_user, "custom_metric", 1)
```

## 🔄 **Migration Strategy**

### **Phase 1: Core Features (Completed)**
- ✅ Classroom creation
- ✅ Exam creation
- ✅ Competition creation

### **Phase 2: Advanced Features (To Implement)**
```python
# AI Question Generation Routes
@router.post("/exams/{exam_id}/questions/generate-ai")
def generate_exam_ai_questions(
    exam_id: UUID,
    count: int,
    db: Session = Depends(get_db),
    subscription_check = Depends(require_ai_features())
):
    # Implementation here
    pass

# Advanced Analytics Routes
@router.get("/classrooms/{classroom_id}/analytics")
def get_classroom_analytics(
    classroom_id: UUID,
    db: Session = Depends(get_db),
    subscription_check = Depends(require_advanced_analytics())
):
    # Implementation here
    pass

# Bulk Operations
@router.post("/questions/bulk-create")
def bulk_create_questions(
    questions: List[QuestionCreate],
    db: Session = Depends(get_db),
    subscription_check = Depends(require_subscription_feature(
        FeaturePermission.BULK_OPERATIONS
    ))
):
    # Implementation here
    pass
```

### **Phase 3: Institute Features**
```python
# Teacher Management
@router.post("/institutes/{institute_id}/teachers")
def add_teacher_to_institute(
    institute_id: UUID,
    teacher_id: UUID,
    db: Session = Depends(get_db),
    subscription_check = Depends(require_subscription_feature(
        FeaturePermission.TEACHER_MANAGEMENT
    ))
):
    # Implementation here
    pass

# API Access
@router.get("/api/institutes/{institute_id}/data")
def get_institute_api_data(
    institute_id: UUID,
    db: Session = Depends(get_db),
    subscription_check = Depends(require_subscription_feature(
        FeaturePermission.API_ACCESS
    ))
):
    # Implementation here
    pass
```

## 🛠️ **Setup Instructions**

### **1. Initialize Hardcoded Plans**
```bash
python app/scripts/setup_default_subscriptions.py
```

### **2. Update Route Imports**
```python
# Add to route files
from config.subscription_permission import (
    require_classroom_creation,
    require_exam_creation,
    require_competition_creation,
    update_usage_counter
)
```

### **3. Replace Dependencies**
```python
# Old way
_ = Depends(require_type("teacher"))

# New way
subscription_check = Depends(require_classroom_creation())
current_user = subscription_check["user"]
```

### **4. Add Usage Tracking**
```python
# After successful operation
update_usage_counter(db, current_user, "metric_name", increment)
```

## 🔍 **Error Handling**

### **Subscription Errors**
```python
from config.subscription_permission import SubscriptionError

# Automatic error responses:
# 403: "Feature not available in your current plan"
# 403: "You have reached your limit (5/5). Please upgrade"
# 403: "Subscription expired. Please renew"
```

### **Custom Error Messages**
```python
# The middleware provides helpful upgrade messages
{
    "detail": "AI Question Generation is not available in your Teacher Basic. Please upgrade to one of: Teacher Premium, Teacher Home Tutor"
}
```

## 📈 **Benefits**

### **For Users**
- ✅ Clear feature boundaries
- ✅ Helpful upgrade prompts
- ✅ Usage tracking and warnings
- ✅ Fair usage limits

### **For Platform**
- ✅ Automated subscription enforcement
- ✅ Revenue generation through upgrades
- ✅ Usage analytics and insights
- ✅ Scalable permission system

The subscription middleware system is now ready for deployment across all routes!
