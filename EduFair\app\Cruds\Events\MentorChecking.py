from typing import List, Optional, Dict, Any
import uuid
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc
from fastapi import HTTPException
from datetime import datetime, timezone
from decimal import Decimal

# Import Models
from Models.Events import Event, EventRegistration
from Models.Competitions import (
    CompetitionMentorAssignment, MentorAssignmentStatusEnum,
    CompetitionAnswer, CompetitionQuestion, CompetitionResult
)
from Models.users import User, UserTypeEnum, MentorProfile, MentorInstituteAssociation

# Import Schemas
from Schemas.Institute.Mentor import MentorDetailedOut


def assign_mentor_to_competition(
    db: Session,
    competition_id: uuid.UUID,
    mentor_id: uuid.UUID,
    assigner_id: uuid.UUID,
    assignment_notes: str = None,
    questions_assigned: List[uuid.UUID] = None,
    participants_assigned: List[uuid.UUID] = None,
    estimated_hours: float = None,
    hourly_rate: Decimal = None
) -> Dict[str, Any]:
    """Assign a mentor to check a competition"""
    
    # Verify competition exists and assigner has permission
    competition = db.query(Event).filter(
        Event.id == competition_id,
        Event.is_competition == True
    ).first()
    
    if not competition:
        raise HTTPException(status_code=404, detail="Competition not found")
    
    # Check if assigner has permission (organizer or institute)
    assigner = db.query(User).filter(User.id == assigner_id).first()
    if not assigner:
        raise HTTPException(status_code=404, detail="Assigner not found")
    
    if competition.organizer_id != assigner_id and competition.institute_id != assigner_id:
        raise HTTPException(status_code=403, detail="Only competition organizer can assign mentors")
    
    # Verify mentor exists and is verified
    mentor = db.query(User).options(
        joinedload(User.mentor_profile)
    ).filter(
        User.id == mentor_id,
        User.user_type == UserTypeEnum.mentor
    ).first()
    
    if not mentor:
        raise HTTPException(status_code=404, detail="Mentor not found")
    
    if not mentor.mentor_profile.is_verified:
        raise HTTPException(status_code=400, detail="Mentor must be verified")
    
    # Check if mentor is associated with the institute (if competition is institute-owned)
    if competition.institute_id:
        association = db.query(MentorInstituteAssociation).filter(
            MentorInstituteAssociation.mentor_id == mentor_id,
            MentorInstituteAssociation.institute_id == competition.institute_id,
            MentorInstituteAssociation.status == "active"
        ).first()
        
        if not association:
            raise HTTPException(
                status_code=400, 
                detail="Mentor must be associated with the institute to check its competitions"
            )
    
    # Check if mentor is already assigned
    existing_assignment = db.query(CompetitionMentorAssignment).filter(
        CompetitionMentorAssignment.competition_id == competition_id,
        CompetitionMentorAssignment.mentor_id == mentor_id
    ).first()
    
    if existing_assignment:
        raise HTTPException(status_code=400, detail="Mentor already assigned to this competition")
    
    # Calculate total questions and participants if not specified
    if questions_assigned is None:
        total_questions = db.query(CompetitionQuestion).filter(
            CompetitionQuestion.competition_id == competition_id
        ).count()
    else:
        total_questions = len(questions_assigned)
    
    if participants_assigned is None:
        total_participants = db.query(EventRegistration).filter(
            EventRegistration.event_id == competition_id
        ).count()
    else:
        total_participants = len(participants_assigned)
    
    # Use mentor's hourly rate if not specified
    if hourly_rate is None and mentor.mentor_profile.hourly_rate:
        hourly_rate = mentor.mentor_profile.hourly_rate
    
    # Create assignment
    assignment = CompetitionMentorAssignment(
        competition_id=competition_id,
        mentor_id=mentor_id,
        assigned_by=assigner_id,
        status=MentorAssignmentStatusEnum.ASSIGNED,
        assigned_at=datetime.now(timezone.utc),
        questions_assigned=questions_assigned,
        participants_assigned=participants_assigned,
        estimated_hours=estimated_hours,
        hourly_rate=hourly_rate,
        total_compensation=estimated_hours * hourly_rate if estimated_hours and hourly_rate else None,
        total_questions=total_questions,
        assignment_notes=assignment_notes
    )
    
    db.add(assignment)
    db.commit()
    db.refresh(assignment)
    
    return {
        "assignment_id": str(assignment.id),
        "mentor_id": str(mentor_id),
        "competition_id": str(competition_id),
        "status": assignment.status.value,
        "total_questions": total_questions,
        "estimated_hours": estimated_hours,
        "hourly_rate": float(hourly_rate) if hourly_rate else None
    }


def get_mentor_assignments(
    db: Session,
    mentor_id: uuid.UUID,
    status: str = None,
    skip: int = 0,
    limit: int = 20
) -> List[Dict[str, Any]]:
    """Get mentor's competition assignments"""
    
    query = db.query(CompetitionMentorAssignment).options(
        joinedload(CompetitionMentorAssignment.competition)
    ).filter(CompetitionMentorAssignment.mentor_id == mentor_id)
    
    if status:
        try:
            status_enum = MentorAssignmentStatusEnum(status)
            query = query.filter(CompetitionMentorAssignment.status == status_enum)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid status")
    
    assignments = query.order_by(desc(CompetitionMentorAssignment.assigned_at)).offset(skip).limit(limit).all()
    
    assignment_list = []
    for assignment in assignments:
        competition = assignment.competition
        
        assignment_list.append({
            "assignment_id": str(assignment.id),
            "competition_id": str(competition.id),
            "competition_title": competition.title,
            "competition_start": competition.start_datetime,
            "competition_end": competition.end_datetime,
            "status": assignment.status.value,
            "assigned_at": assignment.assigned_at,
            "accepted_at": assignment.accepted_at,
            "completed_at": assignment.completed_at,
            "total_questions": assignment.total_questions,
            "questions_checked": assignment.questions_checked,
            "progress_percentage": float(assignment.progress_percentage),
            "estimated_hours": float(assignment.estimated_hours) if assignment.estimated_hours else None,
            "hourly_rate": float(assignment.hourly_rate) if assignment.hourly_rate else None,
            "total_compensation": float(assignment.total_compensation) if assignment.total_compensation else None,
            "assignment_notes": assignment.assignment_notes
        })
    
    return assignment_list


def respond_to_assignment(
    db: Session,
    assignment_id: uuid.UUID,
    mentor_id: uuid.UUID,
    accept: bool,
    response_message: str = None
) -> Dict[str, Any]:
    """Mentor responds to assignment (accept/decline)"""
    
    assignment = db.query(CompetitionMentorAssignment).filter(
        CompetitionMentorAssignment.id == assignment_id,
        CompetitionMentorAssignment.mentor_id == mentor_id
    ).first()
    
    if not assignment:
        raise HTTPException(status_code=404, detail="Assignment not found")
    
    if assignment.status != MentorAssignmentStatusEnum.ASSIGNED:
        raise HTTPException(status_code=400, detail="Assignment has already been responded to")
    
    if accept:
        assignment.status = MentorAssignmentStatusEnum.ACCEPTED
        assignment.accepted_at = datetime.now(timezone.utc)
    else:
        assignment.status = MentorAssignmentStatusEnum.DECLINED
    
    # TODO: Store response message if needed
    
    db.commit()
    db.refresh(assignment)
    
    return {
        "assignment_id": str(assignment.id),
        "status": assignment.status.value,
        "accepted_at": assignment.accepted_at,
        "message": "Assignment accepted" if accept else "Assignment declined"
    }


def get_answers_to_check(
    db: Session,
    mentor_id: uuid.UUID,
    competition_id: uuid.UUID = None,
    question_id: uuid.UUID = None,
    skip: int = 0,
    limit: int = 20
) -> List[Dict[str, Any]]:
    """Get answers that need to be checked by mentor"""
    
    # Verify mentor has assignments
    assignment_query = db.query(CompetitionMentorAssignment).filter(
        CompetitionMentorAssignment.mentor_id == mentor_id,
        CompetitionMentorAssignment.status == MentorAssignmentStatusEnum.ACCEPTED
    )
    
    if competition_id:
        assignment_query = assignment_query.filter(
            CompetitionMentorAssignment.competition_id == competition_id
        )
    
    assignments = assignment_query.all()
    
    if not assignments:
        return []
    
    # Get competition IDs mentor is assigned to
    competition_ids = [assignment.competition_id for assignment in assignments]
    
    # Get answers that need checking
    query = db.query(CompetitionAnswer).options(
        joinedload(CompetitionAnswer.question),
        joinedload(CompetitionAnswer.participant)
    ).filter(
        CompetitionAnswer.competition_id.in_(competition_ids),
        CompetitionAnswer.is_checked_by_mentor == False
    )
    
    if question_id:
        query = query.filter(CompetitionAnswer.question_id == question_id)
    
    # Filter by assigned questions/participants if specified
    for assignment in assignments:
        if assignment.questions_assigned:
            query = query.filter(CompetitionAnswer.question_id.in_(assignment.questions_assigned))
        if assignment.participants_assigned:
            query = query.filter(CompetitionAnswer.participant_id.in_(assignment.participants_assigned))
    
    answers = query.order_by(CompetitionAnswer.submitted_at).offset(skip).limit(limit).all()
    
    answer_list = []
    for answer in answers:
        answer_list.append({
            "answer_id": str(answer.id),
            "question_id": str(answer.question_id),
            "participant_id": str(answer.participant_id),
            "competition_id": str(answer.competition_id),
            "question_text": answer.question.question_text,
            "question_type": answer.question.question_type.value,
            "question_marks": answer.question.marks,
            "participant_username": answer.participant.username,
            "answer_text": answer.answer_text,
            "selected_option": answer.selected_option,
            "answer_files": answer.answer_files,
            "submitted_at": answer.submitted_at,
            "time_taken_seconds": answer.time_taken_seconds,
            "ai_score": float(answer.ai_score) if answer.ai_score else None,
            "ai_feedback": answer.ai_feedback,
            "expected_answer": answer.question.expected_answer,
            "question_options": answer.question.options
        })
    
    return answer_list


def submit_mentor_score(
    db: Session,
    answer_id: uuid.UUID,
    mentor_id: uuid.UUID,
    mentor_score: Decimal,
    mentor_feedback: str = None
) -> Dict[str, Any]:
    """Submit mentor score for an answer"""
    
    answer = db.query(CompetitionAnswer).options(
        joinedload(CompetitionAnswer.question)
    ).filter(CompetitionAnswer.id == answer_id).first()
    
    if not answer:
        raise HTTPException(status_code=404, detail="Answer not found")
    
    # Verify mentor is assigned to this competition
    assignment = db.query(CompetitionMentorAssignment).filter(
        CompetitionMentorAssignment.mentor_id == mentor_id,
        CompetitionMentorAssignment.competition_id == answer.competition_id,
        CompetitionMentorAssignment.status == MentorAssignmentStatusEnum.ACCEPTED
    ).first()
    
    if not assignment:
        raise HTTPException(status_code=403, detail="Mentor not assigned to this competition")
    
    # Check if mentor is assigned to this specific question/participant
    if assignment.questions_assigned and answer.question_id not in assignment.questions_assigned:
        raise HTTPException(status_code=403, detail="Mentor not assigned to this question")
    
    if assignment.participants_assigned and answer.participant_id not in assignment.participants_assigned:
        raise HTTPException(status_code=403, detail="Mentor not assigned to this participant")
    
    # Validate score
    max_marks = answer.question.marks
    if mentor_score < 0 or mentor_score > max_marks:
        raise HTTPException(status_code=400, detail=f"Score must be between 0 and {max_marks}")
    
    # Update answer
    answer.mentor_score = mentor_score
    answer.mentor_feedback = mentor_feedback
    answer.is_checked_by_mentor = True
    answer.checked_at = datetime.now(timezone.utc)
    answer.checked_by = mentor_id
    
    # Calculate final score (prefer mentor score over AI score)
    answer.final_score = mentor_score
    
    # Update assignment progress
    assignment.questions_checked += 1
    if assignment.total_questions > 0:
        assignment.progress_percentage = (assignment.questions_checked / assignment.total_questions) * 100
    
    # Check if assignment is completed
    if assignment.progress_percentage >= 100:
        assignment.status = MentorAssignmentStatusEnum.COMPLETED
        assignment.completed_at = datetime.now(timezone.utc)
    
    db.commit()
    
    return {
        "answer_id": str(answer.id),
        "mentor_score": float(mentor_score),
        "final_score": float(answer.final_score),
        "mentor_feedback": mentor_feedback,
        "checked_at": answer.checked_at,
        "assignment_progress": float(assignment.progress_percentage)
    }


def get_mentor_checking_statistics(
    db: Session,
    mentor_id: uuid.UUID,
    competition_id: uuid.UUID = None
) -> Dict[str, Any]:
    """Get mentor's checking statistics"""
    
    # Base query for assignments
    assignment_query = db.query(CompetitionMentorAssignment).filter(
        CompetitionMentorAssignment.mentor_id == mentor_id
    )
    
    if competition_id:
        assignment_query = assignment_query.filter(
            CompetitionMentorAssignment.competition_id == competition_id
        )
    
    assignments = assignment_query.all()
    
    # Calculate statistics
    total_assignments = len(assignments)
    completed_assignments = len([a for a in assignments if a.status == MentorAssignmentStatusEnum.COMPLETED])
    active_assignments = len([a for a in assignments if a.status == MentorAssignmentStatusEnum.ACCEPTED])
    
    total_questions_assigned = sum(a.total_questions for a in assignments)
    total_questions_checked = sum(a.questions_checked for a in assignments)
    
    # Get answer statistics
    answer_query = db.query(CompetitionAnswer).filter(
        CompetitionAnswer.checked_by == mentor_id
    )
    
    if competition_id:
        answer_query = answer_query.filter(CompetitionAnswer.competition_id == competition_id)
    
    total_answers_checked = answer_query.count()
    
    # Average score given
    avg_score = db.query(func.avg(CompetitionAnswer.mentor_score)).filter(
        CompetitionAnswer.checked_by == mentor_id,
        CompetitionAnswer.mentor_score.isnot(None)
    ).scalar() or 0
    
    # Total compensation
    total_compensation = sum(
        float(a.total_compensation) for a in assignments 
        if a.total_compensation and a.status == MentorAssignmentStatusEnum.COMPLETED
    )
    
    return {
        "total_assignments": total_assignments,
        "completed_assignments": completed_assignments,
        "active_assignments": active_assignments,
        "total_questions_assigned": total_questions_assigned,
        "total_questions_checked": total_questions_checked,
        "total_answers_checked": total_answers_checked,
        "completion_rate": (total_questions_checked / total_questions_assigned * 100) if total_questions_assigned > 0 else 0,
        "average_score_given": float(avg_score),
        "total_compensation_earned": total_compensation
    }
