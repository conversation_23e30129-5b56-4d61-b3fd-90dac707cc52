-- Complete enum migration fix for PostgreSQL database
-- This script completely removes all enum types and prepares for clean migration

-- =====================================================
-- STEP 1: Check what exists in the database
-- =====================================================

-- Check all existing enum types
SELECT 'All existing enum types:' as info;
SELECT
    n.nspname as schema_name,
    t.typname as enum_name,
    t.oid as type_oid
FROM pg_type t
JOIN pg_namespace n ON t.typnamespace = n.oid
WHERE t.typtype = 'e'
ORDER BY n.nspname, t.typname;

-- Check all tables using enum types
SELECT 'Tables using enum types:' as info;
SELECT
    table_schema,
    table_name,
    column_name,
    udt_name as enum_type
FROM information_schema.columns
WHERE udt_name LIKE '%enum%'
ORDER BY table_schema, table_name, column_name;

-- =====================================================
-- STEP 2: Complete cleanup (UNCOMMENT TO EXECUTE)
-- =====================================================

-- Drop all enum types that might conflict
-- IMPORTANT: Uncomment these lines to execute the cleanup

-- DROP TYPE IF EXISTS usertypeenum CASCADE;
-- DROP TYPE IF EXISTS eventstatusenum CASCADE;
-- DROP TYPE IF EXISTS registrationstatusenum CASCADE;
-- DROP TYPE IF EXISTS taskstatusenum CASCADE;
-- DROP TYPE IF EXISTS examstatusenum CASCADE;
-- DROP TYPE IF EXISTS questiontypeenum CASCADE;
-- DROP TYPE IF EXISTS difficultylevelenum CASCADE;
-- DROP TYPE IF EXISTS competitionstatusenum CASCADE;
-- DROP TYPE IF EXISTS mentorassignmentstatusenum CASCADE;
-- DROP TYPE IF EXISTS mentorverificationstatusenum CASCADE;
-- DROP TYPE IF EXISTS associationstatusenum CASCADE;
-- DROP TYPE IF EXISTS associationtypeenum CASCADE;

-- =====================================================
-- STEP 3: Check alembic state
-- =====================================================

-- Check if alembic_version table exists
SELECT 'Alembic version table exists:' as info;
SELECT EXISTS (
    SELECT 1 FROM information_schema.tables
    WHERE table_name = 'alembic_version'
) as table_exists;

-- Get current alembic version if it exists
SELECT 'Current alembic version:' as info;
SELECT version_num FROM alembic_version WHERE EXISTS (
    SELECT 1 FROM information_schema.tables
    WHERE table_name = 'alembic_version'
);

-- =====================================================
-- STEP 4: Manual cleanup commands (if needed)
-- =====================================================

-- If the above doesn't work, try these manual commands:

-- Force drop all enum types in all schemas
-- SELECT 'DROP TYPE IF EXISTS ' || n.nspname || '.' || t.typname || ' CASCADE;'
-- FROM pg_type t
-- JOIN pg_namespace n ON t.typnamespace = n.oid
-- WHERE t.typtype = 'e' AND t.typname LIKE '%enum%';

-- Reset alembic version (DANGEROUS - only for development)
-- DELETE FROM alembic_version;

-- =====================================================
-- INSTRUCTIONS FOR COMPLETE FIX:
-- =====================================================

/*
1. First, run this script to see what exists:
   psql -d your_database -f fix_enum_migration.sql

2. If you see conflicting enum types, uncomment the DROP TYPE commands above and run again

3. Reset alembic state:
   alembic stamp head

4. Run migrations:
   alembic upgrade head

5. If you still get errors, try the nuclear option:
   - Drop the entire database: DROP DATABASE your_database;
   - Create new database: CREATE DATABASE your_database;
   - Run migrations: alembic upgrade head

6. For development environments, you can also try:
   - Delete all migration files except __init__.py in alembic/versions/
   - Generate new initial migration: alembic revision --autogenerate -m "Initial migration"
   - Run: alembic upgrade head
*/
